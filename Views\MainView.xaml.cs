using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using MEP.Pacifire.ViewModels;
using MessageBox = System.Windows.MessageBox;

namespace MEP.Pacifire.Views
{
    /// <summary>
    /// Interaction logic for MainView.xaml
    /// Main window for the Passive Fire Stopping Solution Exporter
    /// </summary>
    public partial class MainView : Window
    {
        /// <summary>
        /// Constructor for MainView with ViewModel
        /// </summary>
        /// <param name="viewModel">Main ViewModel instance</param>
        public MainView(MainViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        /// <summary>
        /// Handle window closing to ensure proper cleanup
        /// </summary>
        /// <param name="e">Cancel event args</param>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // Check if operation is in progress
            if (DataContext is MainViewModel viewModel && viewModel.IsOperationInProgress)
            {
                var result = MessageBox.Show(
                    "An operation is currently in progress. Are you sure you want to close the application?",
                    "Operation in Progress",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                // Cancel the operation
                viewModel.CancelOperationCommand.Execute(null);
            }

            base.OnClosing(e);
        }

        /// <summary>
        /// Validates numeric input for adjacency threshold TextBox
        /// </summary>
        /// <param name="sender">TextBox sender</param>
        /// <param name="e">Text input event args</param>
        private void TextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only digits and decimal point
            var regex = new Regex(@"^[0-9.]+$");
            e.Handled = !regex.IsMatch(e.Text);

            // Additional validation for decimal point
            if (e.Text == "." && sender is System.Windows.Controls.TextBox textBox)
            {
                // Don't allow multiple decimal points
                e.Handled = textBox.Text.Contains(".");
            }
        }
    }


}
