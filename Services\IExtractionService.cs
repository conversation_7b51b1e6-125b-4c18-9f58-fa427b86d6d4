using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Interface for extracting fire stopping elements, services, and structures from Revit models.
    /// Handles coordinate transformation and filtering based on user selections.
    /// </summary>
    public interface IExtractionService
    {
        /// <summary>
        /// Extracts fire stopping elements from selected linked models
        /// </summary>
        /// <param name="document">Host Revit document</param>
        /// <param name="filterSettings">Filter settings for extraction</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Collection of fire stopping elements</returns>
        IEnumerable<FireStoppingElement> ExtractFireStoppingElements(
            Document document,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Extracts service elements connected to fire stopping elements
        /// </summary>
        /// <param name="document">Host Revit document</param>
        /// <param name="fireStoppingElements">Fire stopping elements to find connections for</param>
        /// <param name="filterSettings">Filter settings for extraction</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Collection of service elements</returns>
        IEnumerable<ServiceElement> ExtractConnectedServices(
            Document document,
            IEnumerable<FireStoppingElement> fireStoppingElements,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Extracts structural elements (walls, floors) from selected linked models
        /// </summary>
        /// <param name="document">Host Revit document</param>
        /// <param name="filterSettings">Filter settings for extraction</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Collection of structural elements</returns>
        IEnumerable<StructuralElement> ExtractStructuralElements(
            Document document,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all available levels in the document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Collection of level filters</returns>
        IEnumerable<LevelFilter> GetAvailableLevels(Document document);

        /// <summary>
        /// Gets all available categories relevant for fire stopping analysis
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Collection of category filters</returns>
        IEnumerable<CategoryFilter> GetAvailableCategories(Document document);

        /// <summary>
        /// Gets all loaded linked models in the document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Collection of linked model filters</returns>
        IEnumerable<LinkedModelFilter> GetAvailableLinkedModels(Document document);

        /// <summary>
        /// Validates that the document is suitable for fire stopping analysis
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Validation result with any issues</returns>
        ValidationResult ValidateDocument(Document document);

        /// <summary>
        /// Event raised when extraction progress changes
        /// </summary>
        event EventHandler<ExtractionProgressEventArgs> ProgressChanged;

        /// <summary>
        /// Event raised when extraction status changes
        /// </summary>
        event EventHandler<ExtractionStatusEventArgs> StatusChanged;
    }

    /// <summary>
    /// Event arguments for extraction progress updates
    /// </summary>
    public class ExtractionProgressEventArgs : EventArgs
    {
        public int Current { get; set; }
        public int Total { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public double PercentComplete => Total > 0 ? (double)Current / Total * 100 : 0;
    }

    /// <summary>
    /// Event arguments for extraction status updates
    /// </summary>
    public class ExtractionStatusEventArgs : EventArgs
    {
        public string Status { get; set; } = string.Empty;
        public bool IsError { get; set; }
        public Exception? Exception { get; set; }
    }

    /// <summary>
    /// Represents the result of document validation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();

        public bool HasIssues => Issues.Count > 0;
        public bool HasWarnings => Warnings.Count > 0;

        public string Summary
        {
            get
            {
                if (IsValid && !HasWarnings)
                    return "Document is valid for fire stopping analysis.";

                var summary = new List<string>();
                if (!IsValid)
                    summary.Add($"{Issues.Count} issue(s) found");
                if (HasWarnings)
                    summary.Add($"{Warnings.Count} warning(s) found");

                return string.Join(", ", summary);
            }
        }
    }
}
