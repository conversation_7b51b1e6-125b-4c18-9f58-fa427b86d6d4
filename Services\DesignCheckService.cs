using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms; // For Application.DoEvents()
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using MEP.Pacifire.Helpers;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Service for performing design checks on fire stopping elements.
    /// Validates spatial relationships and identifies placement issues using accurate geometry.
    /// </summary>
    public class DesignCheckService : IDesignCheckService
    {
        private readonly IGeometryHelper _geometryHelper;
        private readonly ISpatialHelper _spatialHelper;

        public event EventHandler<DesignCheckProgressEventArgs>? ProgressChanged;
        public event EventHandler<DesignCheckStatusEventArgs>? StatusChanged;

        public DesignCheckService(IGeometryHelper geometryHelper, ISpatialHelper spatialHelper)
        {
            _geometryHelper = geometryHelper ?? throw new ArgumentNullException(nameof(geometryHelper));
            _spatialHelper = spatialHelper ?? throw new ArgumentNullException(nameof(spatialHelper));
        }

        /// <summary>
        /// Performs all design checks on fire stopping elements
        /// </summary>
        public IEnumerable<FireStoppingElement> PerformDesignChecks(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            IEnumerable<ServiceElement> serviceElements,
            IEnumerable<StructuralElement> structuralElements,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default)
        {
            OnStatusChanged("Starting design checks...", false, "All");
            
            var fireStoppingList = fireStoppingElements.ToList();
            var serviceList = serviceElements.ToList();
            var structuralList = structuralElements.ToList();
            
            var totalElements = fireStoppingList.Count;
            var currentElement = 0;

            // Create spatial index for performance optimization
            OnStatusChanged("Building spatial index...", false, "Optimization");
            var spatialIndex = _spatialHelper.CreateSpatialIndex(fireStoppingList, structuralList, serviceList);

            foreach (var fireStoppingElement in fireStoppingList)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                currentElement++;
                OnProgressChanged(currentElement, totalElements, $"Analyzing element {currentElement}", fireStoppingElement.ElementId.ToString());
                Application.DoEvents(); // Allow UI to update

                try
                {
                    // Reset previous check results
                    fireStoppingElement.DesignCheckResult.Reset();

                    // Perform all checks
                    PerformAllChecksForElement(
                        fireStoppingElement,
                        fireStoppingList,
                        serviceList,
                        structuralList,
                        filterSettings,
                        spatialIndex,
                        cancellationToken);
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"Error checking element {fireStoppingElement.ElementId}: {ex.Message}", true, "Error");
                    
                    // Mark all checks as failed due to error
                    fireStoppingElement.DesignCheckResult.AddCustomCheck("ProcessingError", true, ex.Message);
                }
            }

            var failureCount = fireStoppingList.Count(x => x.HasFailures);
            OnStatusChanged($"Design checks completed. {failureCount} elements have failures.", false, "Complete");
            
            return fireStoppingList;
        }

        /// <summary>
        /// Performs the "Not Touching Wall" check for a single element
        /// </summary>
        public bool CheckNotTouchingWall(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements)
        {
            if (fireStoppingElement.TransformedSolid == null)
            {
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingWall", "No geometry available for intersection check");
                return true; // Fail the check
            }

            var nearestResult = FindNearestStructuralElement(fireStoppingElement, structuralElements);
            fireStoppingElement.DesignCheckResult.DistanceToNearestWall = nearestResult.Distance;

            // Check for intersection with any structural element
            foreach (var structural in structuralElements)
            {
                if (structural.TransformedSolid == null) continue;

                try
                {
                    var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                        fireStoppingElement.TransformedSolid,
                        structural.TransformedSolid,
                        BooleanOperationsType.Intersect);

                    if (intersection != null && intersection.Volume > 1e-6) // Small tolerance for floating point
                    {
                        fireStoppingElement.AdjacentStructure = structural;
                        fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingWall",
                            $"Element intersects with {structural.StructureType}: {structural.DisplayName}");
                        return false; // Pass the check (element IS touching wall)
                    }
                }
                catch (Exception ex)
                {
                    // Log but continue checking other elements
                    System.Diagnostics.Debug.WriteLine($"Intersection check failed: {ex.Message}");
                }
            }

            // No intersection found
            var message = nearestResult.NearestElement != null
                ? $"Nearest {nearestResult.NearestElement.StructureType} is {nearestResult.Distance:F1}mm away"
                : "No structural elements found nearby";

            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingWall", message);
            return true; // Fail the check (element is NOT touching wall)
        }

        /// <summary>
        /// Performs the "Not Touching Service" check for a single element
        /// </summary>
        public bool CheckNotTouchingService(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements)
        {
            if (fireStoppingElement.BoundingBox == null)
            {
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService", "No bounding box available for fire stopping element");
                return true; // Fail the check
            }

            try
            {
                // Get the bounding box of the fire stopping element and scale it by 0.2
                var originalBBox = fireStoppingElement.BoundingBox;
                var scaledBBox = ScaleBoundingBox(originalBBox, 0.2);

                // Check each service element
                foreach (var service in serviceElements)
                {
                    if (service == null) continue;

                    try
                    {
                        // Check if LocationPoint is inside the scaled bounding box
                        if (service.LocationPoint != null && IsPointInsideBoundingBox(service.LocationPoint, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service LocationPoint inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }

                        // Check if EndPoint1 is inside the scaled bounding box
                        if (service.EndPoint1 != null && IsPointInsideBoundingBox(service.EndPoint1, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service EndPoint1 inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }

                        // Check if EndPoint2 is inside the scaled bounding box
                        if (service.EndPoint2 != null && IsPointInsideBoundingBox(service.EndPoint2, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service EndPoint2 inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Service point check failed for element {service.ElementId}: {ex.Message}");
                        continue;
                    }
                }

                // If no service points found inside the bounding box, fail the check
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                    "No service elements found within fire stopping area");
                return true; // Fail the check
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CheckNotTouchingService failed: {ex.Message}");
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                    $"Bounding box check failed: {ex.Message}");
                return true; // Fail the check
            }
        }

        /// <summary>
        /// Performs the "Clashing" check for a single element
        /// </summary>
        public (bool IsClashing, IEnumerable<ElementId> ClashingElements) CheckClashing(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements)
        {
            var clashingElements = new List<ElementId>();

            if (fireStoppingElement.TransformedSolid == null)
            {
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Clashing", "No geometry available for clash check");
                return (false, clashingElements);
            }

            foreach (var otherElement in otherFireStoppingElements)
            {
                // Skip self
                if (otherElement.ElementId == fireStoppingElement.ElementId) continue;

                if (otherElement.TransformedSolid == null) continue;

                try
                {
                    var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                        fireStoppingElement.TransformedSolid,
                        otherElement.TransformedSolid,
                        BooleanOperationsType.Intersect);

                    if (intersection != null && intersection.Volume > 1e-6)
                    {
                        clashingElements.Add(otherElement.ElementId);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Clash check failed: {ex.Message}");
                }
            }

            var isClashing = clashingElements.Count > 0;
            if (isClashing)
            {
                fireStoppingElement.DesignCheckResult.ClashingElements = clashingElements.ToList();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Clashing",
                    $"Element clashes with {clashingElements.Count} other fire stopping element(s)");
            }

            return (isClashing, clashingElements);
        }

        /// <summary>
        /// Performs the "Adjacent" check for a single element
        /// </summary>
        public (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements) CheckAdjacent(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements,
            double adjacencyThreshold = 300.0)
        {
            var adjacentElements = new List<ElementId>();
            var distances = new List<double>();

            foreach (var otherElement in otherFireStoppingElements)
            {
                // Skip self
                if (otherElement.ElementId == fireStoppingElement.ElementId) continue;

                var distance = CalculateGeometricDistance(fireStoppingElement, otherElement);
                if (distance <= adjacencyThreshold && distance > 1e-6) // Exclude exact overlaps (clashes)
                {
                    adjacentElements.Add(otherElement.ElementId);
                    distances.Add(distance);
                }
            }

            // Update nearest fire stopping distance
            if (distances.Count > 0)
            {
                fireStoppingElement.DesignCheckResult.DistanceToNearestFireStopping = distances.Min();
            }

            var isAdjacent = adjacentElements.Count > 0;
            if (isAdjacent)
            {
                fireStoppingElement.DesignCheckResult.AdjacentElements = adjacentElements.ToList();
                var minDistance = distances.Min();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Adjacent",
                    $"Element is within {adjacencyThreshold}mm of {adjacentElements.Count} other fire stopping element(s). Nearest surface distance: {minDistance:F1}mm");
            }

            return (isAdjacent, adjacentElements);
        }

        /// <summary>
        /// Calculates the distance between two fire stopping elements
        /// </summary>
        public double CalculateDistance(FireStoppingElement element1, FireStoppingElement element2)
        {
            return CalculateDistance(element1.LocationPoint, element2.LocationPoint);
        }

        /// <summary>
        /// Finds the nearest structural element to a fire stopping element
        /// </summary>
        public (StructuralElement? NearestElement, double Distance) FindNearestStructuralElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements)
        {
            StructuralElement? nearestElement = null;
            var minDistance = double.MaxValue;

            foreach (var structural in structuralElements)
            {
                var distance = CalculateDistance(fireStoppingElement.LocationPoint, structural.LocationPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestElement = structural;
                }
            }

            return (nearestElement, minDistance);
        }

        /// <summary>
        /// Finds the nearest service element to a fire stopping element
        /// </summary>
        public (ServiceElement? NearestElement, double Distance) FindNearestServiceElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements)
        {
            ServiceElement? nearestElement = null;
            var minDistance = double.MaxValue;

            foreach (var service in serviceElements)
            {
                var distance = CalculateDistance(fireStoppingElement.LocationPoint, service.LocationPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestElement = service;
                }
            }

            return (nearestElement, minDistance);
        }

        /// <summary>
        /// Finds the nearest service element within the specified radius by checking distances to service endpoints
        /// </summary>
        /// <param name="fireStoppingElement">The fire stopping element to search from</param>
        /// <param name="serviceElements">Collection of service elements to search</param>
        /// <param name="radius">Search radius in millimeters</param>
        /// <returns>The nearest service element within radius, or null if none found</returns>
        public ServiceElement? FindNearestServiceByRadius(FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements, double radius)
        {
            if (fireStoppingElement?.LocationPoint == null)
                return null;

            ServiceElement? nearestService = null;
            var minDistance = double.MaxValue;
            var fireStoppingLocation = fireStoppingElement.LocationPoint;

            foreach (var service in serviceElements)
            {
                if (service?.EndPoint1 == null || service?.EndPoint2 == null)
                    continue;

                try
                {
                    // Calculate distance to both endpoints of the service element
                    var distanceToEndPoint1 = CalculateDistance(fireStoppingLocation, service.EndPoint1);
                    var distanceToEndPoint2 = CalculateDistance(fireStoppingLocation, service.EndPoint2);

                    // Get the minimum distance to either endpoint
                    var minDistanceToService = Math.Min(distanceToEndPoint1, distanceToEndPoint2);

                    // Check if this service is within radius and closer than previous candidates
                    if (minDistanceToService <= radius && minDistanceToService < minDistance)
                    {
                        nearestService = service;
                        minDistance = minDistanceToService;

                        // Store which endpoint is closest for reference
                        var closestEndpoint = distanceToEndPoint1 <= distanceToEndPoint2
                            ? service.EndPoint1
                            : service.EndPoint2;

                        // Update the service's location point to the closest endpoint for consistency
                        service.LocationPoint = closestEndpoint;
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error calculating distance to service {service.ElementId}: {ex.Message}");
                    continue;
                }
            }

            return nearestService;
        }

        /// <summary>
        /// Gets detailed information about the closest service endpoint to a fire stopping element
        /// </summary>
        /// <param name="fireStoppingElement">The fire stopping element</param>
        /// <param name="serviceElement">The service element to analyze</param>
        /// <returns>Tuple containing (closest endpoint, distance to closest endpoint, is endpoint 1)</returns>
        public (XYZ ClosestEndpoint, double Distance, bool IsEndpoint1) GetClosestServiceEndpoint(
            FireStoppingElement fireStoppingElement, ServiceElement serviceElement)
        {
            if (fireStoppingElement?.LocationPoint == null ||
                serviceElement?.EndPoint1 == null ||
                serviceElement?.EndPoint2 == null)
            {
                return (XYZ.Zero, double.MaxValue, false);
            }

            var fireStoppingLocation = fireStoppingElement.LocationPoint;
            var distanceToEndPoint1 = CalculateDistance(fireStoppingLocation, serviceElement.EndPoint1);
            var distanceToEndPoint2 = CalculateDistance(fireStoppingLocation, serviceElement.EndPoint2);

            if (distanceToEndPoint1 <= distanceToEndPoint2)
            {
                return (serviceElement.EndPoint1, distanceToEndPoint1, true);
            }
            else
            {
                return (serviceElement.EndPoint2, distanceToEndPoint2, false);
            }
        }

        /// <summary>
        /// Finds the nearest service element to a fire stopping element
        /// </summary>
        public (ServiceElement? NearestElement, double Distance) FindNearestServiceElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements)
        {
            ServiceElement? nearestElement = null;
            var minDistance = double.MaxValue;

            foreach (var service in serviceElements)
            {
                var distance = CalculateDistance(fireStoppingElement.LocationPoint, service.LocationPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestElement = service;
                }
            }

            return (nearestElement, minDistance);
        }

        #region Private Helper Methods

        private void PerformAllChecksForElement(
            FireStoppingElement fireStoppingElement,
            List<FireStoppingElement> allFireStoppingElements,
            List<ServiceElement> serviceElements,
            List<StructuralElement> structuralElements,
            FilterSettings filterSettings,
            object spatialIndex,
            CancellationToken cancellationToken)
        {
            // Perform all four standard checks
            fireStoppingElement.DesignCheckResult.NotTouchingWall =
                CheckNotTouchingWall(fireStoppingElement, structuralElements);

            fireStoppingElement.DesignCheckResult.NotTouchingService =
                CheckNotTouchingService(fireStoppingElement, serviceElements);

            var clashResult = CheckClashing(fireStoppingElement, allFireStoppingElements);
            fireStoppingElement.DesignCheckResult.Clashing = clashResult.IsClashing;

            var adjacentResult = CheckAdjacent(fireStoppingElement, allFireStoppingElements, filterSettings.AdjacencyThreshold);
            fireStoppingElement.DesignCheckResult.Adjacent = adjacentResult.IsAdjacent;

            // Update timestamp
            fireStoppingElement.DesignCheckResult.CheckedAt = DateTime.Now;
        }

        private double CalculateDistance(XYZ point1, XYZ point2)
        {
            if (point1 == null || point2 == null) return double.MaxValue;

            // Convert from feet to millimeters
            return point1.DistanceTo(point2) * 304.8;
        }

        /// <summary>
        /// Scales a bounding box by the specified factor around its center
        /// </summary>
        /// <param name="originalBBox">The original bounding box</param>
        /// <param name="scaleFactor">Scale factor (e.g., 0.2 for 20% of original size)</param>
        /// <returns>Scaled bounding box</returns>
        private BoundingBoxXYZ ScaleBoundingBox(BoundingBoxXYZ originalBBox, double scaleFactor)
        {
            if (originalBBox == null) return null;

            try
            {
                // Calculate the center of the bounding box
                var center = (originalBBox.Min + originalBBox.Max) / 2.0;

                // Calculate the half-dimensions of the original box
                var halfWidth = (originalBBox.Max.X - originalBBox.Min.X) / 2.0;
                var halfHeight = (originalBBox.Max.Y - originalBBox.Min.Y) / 2.0;
                var halfDepth = (originalBBox.Max.Z - originalBBox.Min.Z) / 2.0;

                // Scale the half-dimensions
                var scaledHalfWidth = halfWidth * scaleFactor;
                var scaledHalfHeight = halfHeight * scaleFactor;
                var scaledHalfDepth = halfDepth * scaleFactor;

                // Create the scaled bounding box
                var scaledBBox = new BoundingBoxXYZ
                {
                    Min = new XYZ(
                        center.X - scaledHalfWidth,
                        center.Y - scaledHalfHeight,
                        center.Z - scaledHalfDepth),
                    Max = new XYZ(
                        center.X + scaledHalfWidth,
                        center.Y + scaledHalfHeight,
                        center.Z + scaledHalfDepth)
                };

                return scaledBBox;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error scaling bounding box: {ex.Message}");
                return originalBBox; // Return original if scaling fails
            }
        }

        /// <summary>
        /// Checks if a point is inside a bounding box
        /// </summary>
        /// <param name="point">The point to check</param>
        /// <param name="boundingBox">The bounding box</param>
        /// <returns>True if point is inside the bounding box</returns>
        private bool IsPointInsideBoundingBox(XYZ point, BoundingBoxXYZ boundingBox)
        {
            if (point == null || boundingBox == null) return false;

            try
            {
                return point.X >= boundingBox.Min.X && point.X <= boundingBox.Max.X &&
                       point.Y >= boundingBox.Min.Y && point.Y <= boundingBox.Max.Y &&
                       point.Z >= boundingBox.Min.Z && point.Z <= boundingBox.Max.Z;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking point inside bounding box: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Calculates the geometric distance between two fire stopping elements (surface-to-surface)
        /// </summary>
        private double CalculateGeometricDistance(FireStoppingElement element1, FireStoppingElement element2)
        {
            // If either element doesn't have geometry, fall back to center-point distance
            if (element1.TransformedSolid == null || element2.TransformedSolid == null)
            {
                return CalculateDistance(element1, element2);
            }

            try
            {
                // First check if they intersect (distance = 0)
                var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                    element1.TransformedSolid,
                    element2.TransformedSolid,
                    BooleanOperationsType.Intersect);

                if (intersection != null && intersection.Volume > 1e-6)
                {
                    return 0.0; // Elements are touching/overlapping
                }

                // Calculate minimum distance between the two solids
                return CalculateMinimumDistanceBetweenSolids(element1.TransformedSolid, element2.TransformedSolid);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Geometric distance calculation failed: {ex.Message}");
                // Fall back to center-point distance if geometric calculation fails
                return CalculateDistance(element1, element2);
            }
        }

        /// <summary>
        /// Calculates the minimum distance between two solids by sampling points on their surfaces
        /// </summary>
        private double CalculateMinimumDistanceBetweenSolids(Solid solid1, Solid solid2)
        {
            var minDistance = double.MaxValue;

            try
            {
                // Get faces from both solids
                var faces1 = solid1.Faces.Cast<Face>().ToList();
                var faces2 = solid2.Faces.Cast<Face>().ToList();

                // Sample points on faces of solid1 and find closest points on solid2
                foreach (var face1 in faces1)
                {
                    // Sample points on this face
                    var samplePoints = SamplePointsOnFace(face1, 3); // 3x3 grid = 9 points per face

                    foreach (var point1 in samplePoints)
                    {
                        // Find closest point on solid2
                        foreach (var face2 in faces2)
                        {
                            var closestPoint = face2.Project(point1);
                            if (closestPoint != null)
                            {
                                var distance = point1.DistanceTo(closestPoint.XYZPoint) * 304.8; // Convert to mm

                                if (distance < minDistance)
                                {
                                    minDistance = distance;
                                }
                            }
                        }
                    }
                }

                return minDistance == double.MaxValue ? 0.0 : minDistance;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Solid distance calculation failed: {ex.Message}");
                return 0.0; // Assume touching if calculation fails
            }
        }

        /// <summary>
        /// Samples points on a face surface in a grid pattern
        /// </summary>
        private List<XYZ> SamplePointsOnFace(Face face, int gridSize = 3)
        {
            var points = new List<XYZ>();

            try
            {
                var bbox = face.GetBoundingBox();
                if (bbox == null) return points;

                // Create a grid of UV parameters
                for (int i = 0; i <= gridSize; i++)
                {
                    for (int j = 0; j <= gridSize; j++)
                    {
                        var u = bbox.Min.U + (bbox.Max.U - bbox.Min.U) * i / gridSize;
                        var v = bbox.Min.V + (bbox.Max.V - bbox.Min.V) * j / gridSize;

                        var uv = new UV(u, v);

                        if (face.IsInside(uv))
                        {
                            var point = face.Evaluate(uv);
                            points.Add(point);
                        }
                    }
                }

                // If no points found (shouldn't happen), add center point
                if (points.Count == 0)
                {
                    var centerUV = new UV(
                        (bbox.Min.U + bbox.Max.U) / 2,
                        (bbox.Min.V + bbox.Max.V) / 2);
                    points.Add(face.Evaluate(centerUV));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Face sampling failed: {ex.Message}");
                // Return empty list if sampling fails
            }

            return points;
        }

        private void OnProgressChanged(int current, int total, string operation, string elementId)
        {
            ProgressChanged?.Invoke(this, new DesignCheckProgressEventArgs
            {
                Current = current,
                Total = total,
                CurrentOperation = operation,
                ElementId = elementId
            });
        }

        private void OnStatusChanged(string status, bool isError, string checkType, Exception? exception = null)
        {
            StatusChanged?.Invoke(this, new DesignCheckStatusEventArgs
            {
                Status = status,
                IsError = isError,
                CheckType = checkType,
                Exception = exception
            });
        }

        #endregion
    }
}
