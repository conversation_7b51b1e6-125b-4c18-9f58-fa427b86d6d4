using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;
using Autodesk.Revit.DB;

namespace MEP.Pacifire.Models
{
    /// <summary>
    /// Represents a service element (<PERSON><PERSON>, Duct, Cable Tray) connected to a fire stopping fitting.
    /// Contains parameters relevant for fire stopping analysis.
    /// </summary>
    public partial class ServiceElement : ObservableObject
    {
        /// <summary>
        /// Unique identifier for the Revit element
        /// </summary>
        [ObservableProperty]
        private ElementId _elementId;

        /// <summary>
        /// Reference to the source Revit element
        /// </summary>
        [ObservableProperty]
        private Element _revitElement;

        /// <summary>
        /// Reference to the linked model instance containing this element
        /// </summary>
        [ObservableProperty]
        private RevitLinkInstance _linkInstance;

        /// <summary>
        /// Type of service (Pipe, Duct, Cable Tray, etc.)
        /// </summary>
        [ObservableProperty]
        private ServiceType _serviceType;

        /// <summary>
        /// Size of the service element (diameter, width x height, etc.)
        /// </summary>
        [ObservableProperty]
        private string _size = string.Empty;

        /// <summary>
        /// Material of the service element
        /// </summary>
        [ObservableProperty]
        private string _material = string.Empty;

        /// <summary>
        /// System type (e.g., Supply Air, Return Air, Hot Water, etc.)
        /// </summary>
        [ObservableProperty]
        private string _systemType = string.Empty;

        /// <summary>
        /// System name
        /// </summary>
        [ObservableProperty]
        private string _systemName = string.Empty;

        /// <summary>
        /// Level name where the element is located
        /// </summary>
        [ObservableProperty]
        private string _levelName = string.Empty;

        /// <summary>
        /// Location point in host model coordinates (transformed)
        /// For linear elements, this is typically the midpoint or closest endpoint
        /// </summary>
        [ObservableProperty]
        private XYZ _locationPoint;

        /// <summary>
        /// First endpoint of linear service element in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private XYZ _endPoint1;

        /// <summary>
        /// Second endpoint of linear service element in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private XYZ _endPoint2;

        /// <summary>
        /// Bounding box in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private BoundingBoxXYZ _boundingBox;

        /// <summary>
        /// Solid geometry in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private Solid _transformedSolid;

        /// <summary>
        /// Category of the element (Ducts, Pipes, Cable Trays, etc.)
        /// </summary>
        [ObservableProperty]
        private string _category = string.Empty;

        /// <summary>
        /// Insulation thickness (if applicable)
        /// </summary>
        [ObservableProperty]
        private double _insulationThickness;

        /// <summary>
        /// Working pressure (for pipes)
        /// </summary>
        [ObservableProperty]
        private double _workingPressure;

        /// <summary>
        /// Flow rate or capacity
        /// </summary>
        [ObservableProperty]
        private double _flowRate;

        /// <summary>
        /// Temperature rating
        /// </summary>
        [ObservableProperty]
        private double _temperature;

        /// <summary>
        /// Additional custom parameters that may be added in the future
        /// </summary>
        [ObservableProperty]
        private Dictionary<string, object> _customParameters = new();

        /// <summary>
        /// Timestamp when this element was extracted
        /// </summary>
        [ObservableProperty]
        private DateTime _extractedAt = DateTime.Now;

        /// <summary>
        /// Constructor for creating a new ServiceElement
        /// </summary>
        public ServiceElement()
        {
            ElementId = ElementId.InvalidElementId;
            LocationPoint = XYZ.Zero;
            EndPoint1 = XYZ.Zero;
            EndPoint2 = XYZ.Zero;
            ServiceType = ServiceType.Unknown;
        }

        /// <summary>
        /// Constructor with basic element information
        /// </summary>
        /// <param name="elementId">Revit element ID</param>
        /// <param name="revitElement">Source Revit element</param>
        /// <param name="linkInstance">Source link instance</param>
        /// <param name="serviceType">Type of service</param>
        public ServiceElement(ElementId elementId, Element revitElement, RevitLinkInstance linkInstance, ServiceType serviceType)
        {
            ElementId = elementId;
            RevitElement = revitElement;
            LinkInstance = linkInstance;
            ServiceType = serviceType;
            LocationPoint = XYZ.Zero;
            EndPoint1 = XYZ.Zero;
            EndPoint2 = XYZ.Zero;
        }

        /// <summary>
        /// Gets a display name for this service element
        /// </summary>
        public string DisplayName => !string.IsNullOrEmpty(SystemName) 
            ? $"{ServiceType} - {SystemName} ({Size})" 
            : $"{ServiceType} - {Size}";

        /// <summary>
        /// Gets a formatted size string
        /// </summary>
        public string FormattedSize
        {
            get
            {
                if (string.IsNullOrEmpty(Size))
                    return "Unknown";

                return ServiceType switch
                {
                    ServiceType.Duct => $"{Size} (Duct)",
                    ServiceType.Pipe => $"Ø{Size} (Pipe)",
                    ServiceType.CableTray => $"{Size} (Cable Tray)",
                    ServiceType.Conduit => $"Ø{Size} (Conduit)",
                    _ => Size
                };
            }
        }

        /// <summary>
        /// Adds or updates a custom parameter
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <param name="value">Parameter value</param>
        public void SetCustomParameter(string parameterName, object value)
        {
            CustomParameters[parameterName] = value;
        }

        /// <summary>
        /// Gets a custom parameter value
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value or null if not found</returns>
        public T? GetCustomParameter<T>(string parameterName)
        {
            if (CustomParameters.TryGetValue(parameterName, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default(T);
        }

        /// <summary>
        /// Validates that all required parameters are present
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return ElementId != ElementId.InvalidElementId &&
                   ServiceType != ServiceType.Unknown &&
                   RevitElement != null &&
                   LinkInstance != null;
        }

        /// <summary>
        /// Creates a copy of this element for comparison purposes
        /// </summary>
        /// <returns>A new ServiceElement with copied values</returns>
        public ServiceElement Clone()
        {
            return new ServiceElement(ElementId, RevitElement, LinkInstance, ServiceType)
            {
                Size = Size,
                Material = Material,
                SystemType = SystemType,
                SystemName = SystemName,
                LevelName = LevelName,
                LocationPoint = LocationPoint,
                EndPoint1 = EndPoint1,
                EndPoint2 = EndPoint2,
                BoundingBox = BoundingBox,
                TransformedSolid = TransformedSolid,
                Category = Category,
                InsulationThickness = InsulationThickness,
                WorkingPressure = WorkingPressure,
                FlowRate = FlowRate,
                Temperature = Temperature,
                CustomParameters = new Dictionary<string, object>(CustomParameters),
                ExtractedAt = ExtractedAt
            };
        }
    }

    /// <summary>
    /// Enumeration of service types
    /// </summary>
    public enum ServiceType
    {
        Unknown = 0,
        Duct = 1,
        Pipe = 2,
        CableTray = 3,
        Conduit = 4,
        FlexDuct = 5,
        FlexPipe = 6,
        Other = 99
    }
}
