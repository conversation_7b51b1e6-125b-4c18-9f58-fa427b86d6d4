using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using View = Autodesk.Revit.DB.View;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Helper class for common Revit operations and utilities.
    /// Provides convenience methods for working with Revit API.
    /// </summary>
    public static class RevitHelper
    {
        /// <summary>
        /// Gets all elements of a specific category from a document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="category">Built-in category</param>
        /// <returns>Collection of elements</returns>
        public static IEnumerable<Element> GetElementsByCategory(Document document, BuiltInCategory category)
        {
            try
            {
                return new FilteredElementCollector(document)
                    .OfCategory(category)
                    .WhereElementIsNotElementType()
                    .ToElements();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting elements by category {category}: {ex.Message}");
                return Enumerable.Empty<Element>();
            }
        }

        /// <summary>
        /// Gets all elements of multiple categories from a document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="categories">Built-in categories</param>
        /// <returns>Collection of elements</returns>
        public static IEnumerable<Element> GetElementsByCategories(Document document, params BuiltInCategory[] categories)
        {
            try
            {
                var filter = new ElementMulticategoryFilter(categories);
                return new FilteredElementCollector(document)
                    .WherePasses(filter)
                    .WhereElementIsNotElementType()
                    .ToElements();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting elements by categories: {ex.Message}");
                return Enumerable.Empty<Element>();
            }
        }

        /// <summary>
        /// Gets all linked model instances from a document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Collection of RevitLinkInstance elements</returns>
        public static IEnumerable<RevitLinkInstance> GetLinkedModelInstances(Document document)
        {
            try
            {
                return new FilteredElementCollector(document)
                    .OfClass(typeof(RevitLinkInstance))
                    .Cast<RevitLinkInstance>()
                    .Where(link => link.GetLinkDocument() != null);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting linked model instances: {ex.Message}");
                return Enumerable.Empty<RevitLinkInstance>();
            }
        }

        /// <summary>
        /// Gets all levels from a document, sorted by elevation
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Collection of Level elements sorted by elevation</returns>
        public static IEnumerable<Level> GetLevelsSortedByElevation(Document document)
        {
            try
            {
                return new FilteredElementCollector(document)
                    .OfClass(typeof(Level))
                    .Cast<Level>()
                    .OrderBy(level => level.Elevation);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting levels: {ex.Message}");
                return Enumerable.Empty<Level>();
            }
        }

        /// <summary>
        /// Gets the active view from a UIDocument
        /// </summary>
        /// <param name="uidoc">UI Document</param>
        /// <returns>Active view or null</returns>
        public static View? GetActiveView(UIDocument uidoc)
        {
            try
            {
                return uidoc?.ActiveView;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting active view: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Checks if a document is a family document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>True if document is a family document</returns>
        public static bool IsFamilyDocument(Document document)
        {
            try
            {
                return document.IsFamilyDocument;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the project information from a document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>ProjectInfo element or null</returns>
        public static ProjectInfo? GetProjectInfo(Document document)
        {
            try
            {
                return document.ProjectInformation;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting project info: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the units from a document
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <returns>Units object or null</returns>
        public static Units? GetDocumentUnits(Document document)
        {
            try
            {
                return document.GetUnits();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting document units: {ex.Message}");
                return null;
            }
        }

        ///// <summary>
        ///// Converts a value from internal units (feet) to display units
        ///// </summary>
        ///// <param name="document">Revit document</param>
        ///// <param name="value">Value in internal units</param>
        ///// <param name="unitType">Unit type</param>
        ///// <returns>Value in display units</returns>
        //public static double ConvertFromInternalUnits(Document document, double value, SpecTypeId unitType)
        //{
        //    try
        //    {
        //        var units = GetDocumentUnits(document);
        //        if (units != null)
        //        {
        //            var formatOptions = units.GetFormatOptions(unitType);
        //            return UnitUtils.ConvertFromInternalUnits(value, formatOptions.GetUnitTypeId());
        //        }
        //        return value;
        //    }
        //    catch (Exception ex)
        //    {
        //        System.Diagnostics.Debug.WriteLine($"Error converting from internal units: {ex.Message}");
        //        return value;
        //    }
        //}

        ///// <summary>
        ///// Converts a value from display units to internal units (feet)
        ///// </summary>
        ///// <param name="document">Revit document</param>
        ///// <param name="value">Value in display units</param>
        ///// <param name="unitType">Unit type</param>
        ///// <returns>Value in internal units</returns>
        //public static double ConvertToInternalUnits(Document document, double value, SpecTypeId unitType)
        //{
        //    try
        //    {
        //        var units = GetDocumentUnits(document);
        //        if (units != null)
        //        {
        //            var formatOptions = units.GetFormatOptions(unitType);
        //            return UnitUtils.ConvertToInternalUnits(value, formatOptions.GetUnitTypeId());
        //        }
        //        return value;
        //    }
        //    catch (Exception ex)
        //    {
        //        System.Diagnostics.Debug.WriteLine($"Error converting to internal units: {ex.Message}");
        //        return value;
        //    }
        //}

        /// <summary>
        /// Gets elements within a bounding box
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="boundingBox">Bounding box filter</param>
        /// <returns>Elements within the bounding box</returns>
        public static IEnumerable<Element> GetElementsInBoundingBox(Document document, BoundingBoxXYZ boundingBox)
        {
            try
            {
                var outline = new Outline(boundingBox.Min, boundingBox.Max);
                var filter = new BoundingBoxIntersectsFilter(outline);
                
                return new FilteredElementCollector(document)
                    .WherePasses(filter)
                    .WhereElementIsNotElementType()
                    .ToElements();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting elements in bounding box: {ex.Message}");
                return Enumerable.Empty<Element>();
            }
        }

        /// <summary>
        /// Gets elements that intersect with a specific element
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="element">Reference element</param>
        /// <returns>Elements that intersect with the reference element</returns>
        public static IEnumerable<Element> GetIntersectingElements(Document document, Element element)
        {
            try
            {
                var boundingBox = element.get_BoundingBox(null);
                if (boundingBox == null) return Enumerable.Empty<Element>();

                var outline = new Outline(boundingBox.Min, boundingBox.Max);
                var filter = new BoundingBoxIntersectsFilter(outline);
                
                return new FilteredElementCollector(document)
                    .WherePasses(filter)
                    .WhereElementIsNotElementType()
                    .Where(e => e.Id != element.Id);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting intersecting elements: {ex.Message}");
                return Enumerable.Empty<Element>();
            }
        }

        /// <summary>
        /// Gets the workset name for an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Workset name or empty string</returns>
        public static string GetWorksetName(Element element)
        {
            try
            {
                var worksetId = element.WorksetId;
                if (worksetId != WorksetId.InvalidWorksetId)
                {
                    var worksetTable = element.Document.GetWorksetTable();
                    var workset = worksetTable.GetWorkset(worksetId);
                    return workset.Name;
                }
                return string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting workset name: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Checks if an element is visible in a view
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <param name="view">View to check</param>
        /// <returns>True if element is visible in the view</returns>
        public static bool IsElementVisibleInView(Element element, View view)
        {
            try
            {
                return element.IsHidden(view) == false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking element visibility: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the phase created for an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Phase element or null</returns>
        public static Phase? GetPhaseCreated(Element element)
        {
            try
            {
                var phaseCreatedId = element.CreatedPhaseId;
                if (phaseCreatedId != ElementId.InvalidElementId)
                {
                    return element.Document.GetElement(phaseCreatedId) as Phase;
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting phase created: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the phase demolished for an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Phase element or null</returns>
        public static Phase? GetPhaseDemolished(Element element)
        {
            try
            {
                var phaseDemolishedId = element.DemolishedPhaseId;
                if (phaseDemolishedId != ElementId.InvalidElementId)
                {
                    return element.Document.GetElement(phaseDemolishedId) as Phase;
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting phase demolished: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a transaction with automatic rollback on exception
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="transactionName">Transaction name</param>
        /// <param name="action">Action to execute within transaction</param>
        /// <returns>Transaction result</returns>
        public static TransactionStatus ExecuteInTransaction(Document document, string transactionName, Action action)
        {
            using var transaction = new Transaction(document, transactionName);
            try
            {
                var status = transaction.Start();
                if (status != TransactionStatus.Started)
                {
                    return status;
                }

                action();

                return transaction.Commit();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in transaction '{transactionName}': {ex.Message}");
                if (transaction.GetStatus() == TransactionStatus.Started)
                {
                    transaction.RollBack();
                }
                return TransactionStatus.Error;
            }
        }

        /// <summary>
        /// Creates a transaction group with automatic rollback on exception
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="groupName">Transaction group name</param>
        /// <param name="action">Action to execute within transaction group</param>
        /// <returns>Transaction status</returns>
        public static TransactionStatus ExecuteInTransactionGroup(Document document, string groupName, Action action)
        {
            using var transactionGroup = new TransactionGroup(document, groupName);
            try
            {
                var status = transactionGroup.Start();
                if (status != TransactionStatus.Started)
                {
                    return status;
                }

                action();

                return transactionGroup.Assimilate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in transaction group '{groupName}': {ex.Message}");
                if (transactionGroup.GetStatus() == TransactionStatus.Started)
                {
                    transactionGroup.RollBack();
                }
                return TransactionStatus.Error;
            }
        }
    }
}
