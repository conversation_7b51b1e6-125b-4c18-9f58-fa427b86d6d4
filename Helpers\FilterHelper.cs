using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Helper class for filtering and searching operations.
    /// Provides utilities for element filtering and data manipulation.
    /// </summary>
    public static class FilterHelper
    {
        /// <summary>
        /// Filters fire stopping elements based on filter settings
        /// </summary>
        /// <param name="elements">Source elements</param>
        /// <param name="filterSettings">Filter settings</param>
        /// <returns>Filtered elements</returns>
        public static IEnumerable<FireStoppingElement> ApplyFilters(
            IEnumerable<FireStoppingElement> elements, 
            FilterSettings filterSettings)
        {
            var filtered = elements.AsEnumerable();

            // Level filter
            var selectedLevels = filterSettings.SelectedLevels
                .Where(x => x.IsSelected)
                .Select(x => x.Name)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);

            if (selectedLevels.Any())
            {
                filtered = filtered.Where(e => selectedLevels.Contains(e.LevelName));
            }

            // Category filter
            var selectedCategories = filterSettings.SelectedCategories
                .Where(x => x.IsSelected)
                .Select(x => x.Name)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);

            if (selectedCategories.Any())
            {
                filtered = filtered.Where(e => selectedCategories.Contains(e.Category));
            }

            // Linked model filter
            var selectedModels = filterSettings.SelectedLinkedModels
                .Where(x => x.IsSelected)
                .Select(x => x.Name)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);

            if (selectedModels.Any())
            {
                filtered = filtered.Where(e => e.LinkInstance != null && 
                    selectedModels.Contains(e.LinkInstance.GetLinkDocument()?.Title ?? ""));
            }

            // Failures only filter
            if (filterSettings.ShowFailuresOnly)
            {
                filtered = filtered.Where(e => e.HasFailures);
            }

            // Connected services filter
            if (!filterSettings.IncludeUnconnectedServices)
            {
                filtered = filtered.Where(e => e.ConnectedService != null);
            }

            // Connected structures filter
            if (!filterSettings.IncludeUnconnectedStructures)
            {
                filtered = filtered.Where(e => e.AdjacentStructure != null);
            }

            // Search text filter
            if (!string.IsNullOrEmpty(filterSettings.SearchText))
            {
                var searchTerms = ParseSearchText(filterSettings.SearchText);
                filtered = filtered.Where(e => MatchesSearchTerms(e, searchTerms));
            }

            return filtered;
        }

        /// <summary>
        /// Parses search text into individual terms
        /// </summary>
        /// <param name="searchText">Search text</param>
        /// <returns>Array of search terms</returns>
        public static string[] ParseSearchText(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return Array.Empty<string>();

            return searchText
                .Split(new[] { ' ', ',', ';' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(term => term.Trim().ToLowerInvariant())
                .Where(term => !string.IsNullOrEmpty(term))
                .ToArray();
        }

        /// <summary>
        /// Checks if a fire stopping element matches search terms
        /// </summary>
        /// <param name="element">Fire stopping element</param>
        /// <param name="searchTerms">Search terms</param>
        /// <returns>True if element matches any search term</returns>
        public static bool MatchesSearchTerms(FireStoppingElement element, string[] searchTerms)
        {
            if (searchTerms.Length == 0) return true;

            var searchableText = BuildSearchableText(element).ToLowerInvariant();

            return searchTerms.Any(term => searchableText.Contains(term));
        }

        /// <summary>
        /// Builds searchable text from a fire stopping element
        /// </summary>
        /// <param name="element">Fire stopping element</param>
        /// <returns>Searchable text string</returns>
        public static string BuildSearchableText(FireStoppingElement element)
        {
            var textParts = new List<string>
            {
                element.FamilyName,
                element.TypeName,
                element.BecaTypeMark,
                element.BecaInstMark,
                element.BecaSystemDescription,
                element.BecaFamilyMaterial,
                element.BecaFreeSize,
                element.BecaFamilyOrientation,
                element.BecaFamilyReference,
                element.LevelName,
                element.Category,
                element.ConnectedService?.ServiceType.ToString(),
                element.ConnectedService?.Size,
                element.ConnectedService?.Material,
                element.ConnectedService?.SystemType,
                element.AdjacentStructure?.StructureType.ToString(),
                element.AdjacentStructure?.MaterialType,
                element.AdjacentStructure?.FireRating
            };

            // Add custom parameters
            foreach (var customParam in element.CustomParameters)
            {
                textParts.Add(customParam.Value?.ToString());
            }

            return string.Join(" ", textParts.Where(part => !string.IsNullOrEmpty(part)));
        }

        /// <summary>
        /// Sorts fire stopping elements by specified criteria
        /// </summary>
        /// <param name="elements">Elements to sort</param>
        /// <param name="sortBy">Sort criteria</param>
        /// <param name="ascending">Sort direction</param>
        /// <returns>Sorted elements</returns>
        public static IEnumerable<FireStoppingElement> SortElements(
            IEnumerable<FireStoppingElement> elements, 
            SortCriteria sortBy, 
            bool ascending = true)
        {
            var query = elements.AsQueryable();

            query = sortBy switch
            {
                SortCriteria.ElementId => ascending 
                    ? query.OrderBy(e => e.ElementId.IntegerValue) 
                    : query.OrderByDescending(e => e.ElementId.IntegerValue),
                SortCriteria.FamilyName => ascending 
                    ? query.OrderBy(e => e.FamilyName) 
                    : query.OrderByDescending(e => e.FamilyName),
                SortCriteria.TypeMark => ascending 
                    ? query.OrderBy(e => e.BecaTypeMark) 
                    : query.OrderByDescending(e => e.BecaTypeMark),
                SortCriteria.InstMark => ascending 
                    ? query.OrderBy(e => e.BecaInstMark) 
                    : query.OrderByDescending(e => e.BecaInstMark),
                SortCriteria.Level => ascending 
                    ? query.OrderBy(e => e.LevelName) 
                    : query.OrderByDescending(e => e.LevelName),
                SortCriteria.Category => ascending 
                    ? query.OrderBy(e => e.Category) 
                    : query.OrderByDescending(e => e.Category),
                SortCriteria.FailureCount => ascending 
                    ? query.OrderBy(e => e.DesignCheckResult.FailureCount) 
                    : query.OrderByDescending(e => e.DesignCheckResult.FailureCount),
                SortCriteria.HasFailures => ascending 
                    ? query.OrderBy(e => e.HasFailures) 
                    : query.OrderByDescending(e => e.HasFailures),
                _ => query.OrderBy(e => e.ElementId.IntegerValue)
            };

            return query.AsEnumerable();
        }

        /// <summary>
        /// Groups fire stopping elements by specified criteria
        /// </summary>
        /// <param name="elements">Elements to group</param>
        /// <param name="groupBy">Group criteria</param>
        /// <returns>Grouped elements</returns>
        public static IEnumerable<IGrouping<string, FireStoppingElement>> GroupElements(
            IEnumerable<FireStoppingElement> elements, 
            GroupCriteria groupBy)
        {
            return groupBy switch
            {
                GroupCriteria.Level => elements.GroupBy(e => e.LevelName ?? "Unknown"),
                GroupCriteria.Category => elements.GroupBy(e => e.Category ?? "Unknown"),
                GroupCriteria.FamilyName => elements.GroupBy(e => e.FamilyName ?? "Unknown"),
                GroupCriteria.ServiceType => elements.GroupBy(e => e.ConnectedService?.ServiceType.ToString() ?? "No Service"),
                GroupCriteria.StructureType => elements.GroupBy(e => e.AdjacentStructure?.StructureType.ToString() ?? "No Structure"),
                GroupCriteria.HasFailures => elements.GroupBy(e => e.HasFailures ? "Has Failures" : "No Failures"),
                GroupCriteria.LinkedModel => elements.GroupBy(e => e.LinkInstance?.GetLinkDocument()?.Title ?? "Unknown"),
                _ => elements.GroupBy(e => "All Elements")
            };
        }

        /// <summary>
        /// Filters elements by design check results
        /// </summary>
        /// <param name="elements">Elements to filter</param>
        /// <param name="checkType">Type of check to filter by</param>
        /// <param name="failuresOnly">Show only failures</param>
        /// <returns>Filtered elements</returns>
        public static IEnumerable<FireStoppingElement> FilterByDesignCheck(
            IEnumerable<FireStoppingElement> elements, 
            DesignCheckType checkType, 
            bool failuresOnly = true)
        {
            return checkType switch
            {
                DesignCheckType.NotTouchingWall => elements.Where(e => 
                    failuresOnly ? e.DesignCheckResult.NotTouchingWall : !e.DesignCheckResult.NotTouchingWall),
                DesignCheckType.NotTouchingService => elements.Where(e => 
                    failuresOnly ? e.DesignCheckResult.NotTouchingService : !e.DesignCheckResult.NotTouchingService),
                DesignCheckType.Clashing => elements.Where(e => 
                    failuresOnly ? e.DesignCheckResult.Clashing : !e.DesignCheckResult.Clashing),
                DesignCheckType.Adjacent => elements.Where(e => 
                    failuresOnly ? e.DesignCheckResult.Adjacent : !e.DesignCheckResult.Adjacent),
                DesignCheckType.AnyFailure => elements.Where(e => 
                    failuresOnly ? e.HasFailures : !e.HasFailures),
                _ => elements
            };
        }

        /// <summary>
        /// Gets unique values for a specific property across all elements
        /// </summary>
        /// <param name="elements">Elements to analyze</param>
        /// <param name="property">Property to extract unique values for</param>
        /// <returns>Unique values</returns>
        public static IEnumerable<string> GetUniqueValues(
            IEnumerable<FireStoppingElement> elements, 
            ElementProperty property)
        {
            var values = property switch
            {
                ElementProperty.Level => elements.Select(e => e.LevelName),
                ElementProperty.Category => elements.Select(e => e.Category),
                ElementProperty.FamilyName => elements.Select(e => e.FamilyName),
                ElementProperty.TypeName => elements.Select(e => e.TypeName),
                ElementProperty.ServiceType => elements.Select(e => e.ConnectedService?.ServiceType.ToString()),
                ElementProperty.StructureType => elements.Select(e => e.AdjacentStructure?.StructureType.ToString()),
                ElementProperty.LinkedModel => elements.Select(e => e.LinkInstance?.GetLinkDocument()?.Title),
                _ => Enumerable.Empty<string>()
            };

            return values
                .Where(v => !string.IsNullOrEmpty(v))
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .OrderBy(v => v);
        }

        /// <summary>
        /// Creates a summary of filter results
        /// </summary>
        /// <param name="originalCount">Original element count</param>
        /// <param name="filteredCount">Filtered element count</param>
        /// <param name="filterSettings">Applied filter settings</param>
        /// <returns>Filter summary</returns>
        public static string CreateFilterSummary(int originalCount, int filteredCount, FilterSettings filterSettings)
        {
            var summary = new List<string>();

            if (filteredCount == originalCount)
            {
                summary.Add($"Showing all {originalCount} elements");
            }
            else
            {
                summary.Add($"Showing {filteredCount} of {originalCount} elements");
            }

            var activeFilters = new List<string>();

            var selectedLevels = filterSettings.SelectedLevels.Count(x => x.IsSelected);
            if (selectedLevels > 0)
                activeFilters.Add($"{selectedLevels} level(s)");

            var selectedCategories = filterSettings.SelectedCategories.Count(x => x.IsSelected);
            if (selectedCategories > 0)
                activeFilters.Add($"{selectedCategories} category(ies)");

            var selectedModels = filterSettings.SelectedLinkedModels.Count(x => x.IsSelected);
            if (selectedModels > 0)
                activeFilters.Add($"{selectedModels} linked model(s)");

            if (filterSettings.ShowFailuresOnly)
                activeFilters.Add("failures only");

            if (!string.IsNullOrEmpty(filterSettings.SearchText))
                activeFilters.Add($"search: '{filterSettings.SearchText}'");

            if (activeFilters.Any())
            {
                summary.Add($"Filters: {string.Join(", ", activeFilters)}");
            }

            return string.Join(" | ", summary);
        }
    }

    #region Enumerations

    /// <summary>
    /// Sort criteria for fire stopping elements
    /// </summary>
    public enum SortCriteria
    {
        ElementId,
        FamilyName,
        TypeMark,
        InstMark,
        Level,
        Category,
        FailureCount,
        HasFailures
    }

    /// <summary>
    /// Group criteria for fire stopping elements
    /// </summary>
    public enum GroupCriteria
    {
        Level,
        Category,
        FamilyName,
        ServiceType,
        StructureType,
        HasFailures,
        LinkedModel
    }

    /// <summary>
    /// Design check types for filtering
    /// </summary>
    public enum DesignCheckType
    {
        NotTouchingWall,
        NotTouchingService,
        Clashing,
        Adjacent,
        AnyFailure
    }

    /// <summary>
    /// Element properties for unique value extraction
    /// </summary>
    public enum ElementProperty
    {
        Level,
        Category,
        FamilyName,
        TypeName,
        ServiceType,
        StructureType,
        LinkedModel
    }

    #endregion
}
