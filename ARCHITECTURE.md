# Architecture Documentation

## Overview

The Passive Fire Stopping Solution Exporter is built using modern .NET architecture patterns with a focus on maintainability, testability, and extensibility. The application follows MVVM (Model-View-ViewModel) pattern with dependency injection for loose coupling and clean separation of concerns.

## Architectural Patterns

### MVVM (Model-View-ViewModel)
- **Models**: Data entities representing fire stopping elements, services, and structures
- **Views**: WPF user interface with MaterialDesign styling
- **ViewModels**: Business logic and UI state management using CommunityToolkit.Mvvm

### Dependency Injection
- Microsoft.Extensions.DependencyInjection for service registration
- Interface-based design for testability and flexibility
- Scoped and singleton lifetimes for appropriate service management

### Service Layer Pattern
- Clear separation between UI and business logic
- Dedicated services for extraction, design checks, and export
- Async/await pattern for non-blocking operations

## Core Components

### 1. Models Layer

#### FireStoppingElement
```csharp
public partial class FireStoppingElement : ObservableObject
{
    [ObservableProperty] private ElementId _elementId;
    [ObservableProperty] private string _becaTypeMark;
    [ObservableProperty] private ServiceElement? _connectedService;
    [ObservableProperty] private StructuralElement? _adjacentStructure;
    [ObservableProperty] private DesignCheckResult _designCheckResult;
    // ... additional properties
}
```

**Key Features:**
- ObservableObject base class for property change notifications
- Extensible custom parameters dictionary
- Validation methods for data integrity
- Clone methods for comparison operations

#### ServiceElement & StructuralElement
Similar structure to FireStoppingElement with domain-specific properties:
- Service elements: Type, size, material, system information
- Structural elements: Type, fire rating, thickness, construction details

#### DesignCheckResult
```csharp
public partial class DesignCheckResult : ObservableObject
{
    [ObservableProperty] private bool _notTouchingWall;
    [ObservableProperty] private bool _notTouchingService;
    [ObservableProperty] private bool _clashing;
    [ObservableProperty] private bool _adjacent;
    // ... distance calculations and detailed messages
}
```

### 2. Services Layer

#### IExtractionService
Responsible for extracting elements from Revit models:
```csharp
public interface IExtractionService
{
    Task<IEnumerable<FireStoppingElement>> ExtractFireStoppingElementsAsync(
        Document document, FilterSettings filterSettings, CancellationToken cancellationToken);
    
    Task<IEnumerable<ServiceElement>> ExtractConnectedServicesAsync(
        Document document, IEnumerable<FireStoppingElement> fireStoppingElements, 
        FilterSettings filterSettings, CancellationToken cancellationToken);
    
    Task<IEnumerable<StructuralElement>> ExtractStructuralElementsAsync(
        Document document, FilterSettings filterSettings, CancellationToken cancellationToken);
}
```

**Key Responsibilities:**
- Element extraction from linked models
- Coordinate transformation handling
- Parameter extraction and mapping
- Progress reporting and cancellation support

#### IDesignCheckService
Performs spatial validation and design checks:
```csharp
public interface IDesignCheckService
{
    Task<IEnumerable<FireStoppingElement>> PerformDesignChecksAsync(
        IEnumerable<FireStoppingElement> fireStoppingElements,
        IEnumerable<ServiceElement> serviceElements,
        IEnumerable<StructuralElement> structuralElements,
        FilterSettings filterSettings, CancellationToken cancellationToken);
}
```

**Key Responsibilities:**
- Spatial intersection calculations
- Distance measurements
- Design rule validation
- Performance optimization through spatial indexing

#### IExcelExportService
Handles Excel file generation:
```csharp
public interface IExcelExportService
{
    Task<ExportResult> ExportToExcelAsync(
        IEnumerable<FireStoppingElement> fireStoppingElements,
        FilterSettings filterSettings, string filePath,
        ExportSettings exportSettings, CancellationToken cancellationToken);
}
```

**Key Responsibilities:**
- Multi-sheet Excel workbook creation
- Data formatting and styling
- Metadata and summary generation
- Progress reporting during export

### 3. Helper Classes

#### IGeometryHelper
Critical component for spatial operations:
```csharp
public interface IGeometryHelper
{
    void TransformElementGeometry(Element element, Transform linkTransform, FireStoppingElement targetElement);
    Solid? ExtractSolid(Element element);
    bool DoSolidsIntersect(Solid solid1, Solid solid2);
    double CalculateDistance(XYZ point1, XYZ point2);
    // ... additional geometry operations
}
```

**Key Features:**
- Coordinate transformation between linked models
- Solid geometry extraction and validation
- Intersection detection algorithms
- Distance calculations with unit conversion

#### ISpatialHelper
Performance optimization through spatial indexing:
```csharp
public interface ISpatialHelper
{
    SpatialIndex CreateSpatialIndex(
        IEnumerable<FireStoppingElement> fireStoppingElements,
        IEnumerable<StructuralElement> structuralElements,
        IEnumerable<ServiceElement> serviceElements);
    
    SpatialSearchResult FindNearbyElements(
        SpatialIndex spatialIndex, FireStoppingElement targetElement, double searchDistance);
}
```

**Key Features:**
- Grid-based spatial partitioning
- Efficient proximity searches
- Reduced computational complexity for large datasets
- Configurable grid sizes for optimization

#### IParameterHelper
Handles Revit parameter extraction:
```csharp
public interface IParameterHelper
{
    void ExtractFireStoppingParameters(Element element, FireStoppingElement fireStoppingElement);
    string GetParameterValueAsString(Element element, string parameterName);
    ServiceType DetermineServiceType(Element element);
    // ... additional parameter operations
}
```

**Key Features:**
- Type-safe parameter extraction
- Custom parameter mapping support
- Automatic type determination
- Validation and error handling

### 4. ViewModels Layer

#### MainViewModel
Central ViewModel managing the entire application state:
```csharp
public partial class MainViewModel : ObservableObject
{
    [ObservableProperty] private ObservableCollection<FireStoppingElement> _fireStoppingElements;
    [ObservableProperty] private FilterSettings _filterSettings;
    [ObservableProperty] private bool _isOperationInProgress;
    
    [RelayCommand] private async Task ExtractAndAnalyzeAsync();
    [RelayCommand] private async Task ExportToExcelAsync();
    [RelayCommand] private void ApplyFilters();
}
```

**Key Responsibilities:**
- Orchestrating service calls
- Managing UI state and progress
- Handling user commands
- Data filtering and presentation

## Data Flow

### 1. Extraction Phase
```
User Input → MainViewModel → ExtractionService → RevitAPI
                ↓
ParameterHelper ← GeometryHelper ← Element Processing
                ↓
FireStoppingElement Collection ← Coordinate Transformation
```

### 2. Analysis Phase
```
FireStoppingElements → DesignCheckService → SpatialHelper
                ↓                              ↓
        GeometryHelper ← Intersection Logic ← SpatialIndex
                ↓
        DesignCheckResult Updates
```

### 3. Export Phase
```
FilteredElements → ExcelExportService → ClosedXML
                ↓                        ↓
        ExportSettings → Workbook Creation → File Output
```

## Performance Considerations

### Spatial Indexing
- Grid-based partitioning reduces O(n²) to O(n) for proximity searches
- Configurable grid sizes based on element density
- Memory-efficient storage of spatial relationships

### Coordinate Transformation
- Cached transformation matrices for linked models
- Batch processing of geometry operations
- Lazy evaluation of solid geometry when needed

### Memory Management
- Streaming approach for large datasets
- Disposal of temporary geometry objects
- Efficient collection management in ViewModels

### Async Operations
- Non-blocking UI during long-running operations
- Cancellation token support throughout the pipeline
- Progress reporting for user feedback

## Extensibility Points

### Custom Parameters
```csharp
// Add custom parameter mappings
var parameterHelper = ServiceContainer.GetService<IParameterHelper>();
parameterHelper.SetCustomParameterMappings(customMappings);
```

### Custom Design Checks
```csharp
// Extend DesignCheckResult with custom checks
designCheckResult.AddCustomCheck("CustomRule", failed, message);
```

### Custom Export Formats
```csharp
// Implement IExportService for additional formats
public class PdfExportService : IExportService
{
    // Custom export implementation
}
```

## Error Handling Strategy

### Service Layer
- Comprehensive try-catch blocks with logging
- Graceful degradation for non-critical failures
- Detailed error messages for troubleshooting

### UI Layer
- User-friendly error dialogs
- Progress indication during error recovery
- Validation before expensive operations

### Data Layer
- Input validation at model boundaries
- Null-safe operations throughout
- Default values for missing parameters

## Testing Strategy

### Unit Testing
- Service interfaces enable easy mocking
- Helper classes with minimal dependencies
- Isolated testing of business logic

### Integration Testing
- End-to-end workflow validation
- Revit API integration testing
- Excel export verification

### Performance Testing
- Large dataset handling
- Memory usage monitoring
- Spatial indexing efficiency

## Security Considerations

### File Operations
- Path validation for export operations
- Temporary file cleanup
- User permission verification

### Data Validation
- Input sanitization for search terms
- Parameter value validation
- Safe type conversions

## Deployment Architecture

### Add-in Structure
```
MEP.Pacifire.addin          # Revit add-in manifest
MEP.Pacifire.dll            # Main assembly
Dependencies/               # Third-party libraries
├── ClosedXML.dll
├── MaterialDesignThemes.dll
└── CommunityToolkit.Mvvm.dll
```

### Configuration
- App.config for application settings
- User preferences storage
- Logging configuration

This architecture provides a solid foundation for the Fire Stopping Solution Exporter while maintaining flexibility for future enhancements and modifications.
