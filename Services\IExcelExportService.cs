using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Interface for exporting fire stopping analysis results to Excel format.
    /// Creates structured workbooks with grouped sections and metadata.
    /// </summary>
    public interface IExcelExportService
    {
        /// <summary>
        /// Exports fire stopping analysis results to Excel file
        /// </summary>
        /// <param name="fireStoppingElements">Fire stopping elements to export</param>
        /// <param name="filterSettings">Filter settings used for the analysis</param>
        /// <param name="filePath">Output file path</param>
        /// <param name="exportSettings">Export configuration settings</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Export result with success status and details</returns>
        ExportResult ExportToExcel(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            FilterSettings filterSettings,
            string filePath,
            ExportSettings exportSettings,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a preview of the Excel export structure
        /// </summary>
        /// <param name="fireStoppingElements">Fire stopping elements</param>
        /// <param name="exportSettings">Export settings</param>
        /// <returns>Preview information</returns>
        ExportPreview CreateExportPreview(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            ExportSettings exportSettings);

        /// <summary>
        /// Validates the export file path and settings
        /// </summary>
        /// <param name="filePath">Proposed file path</param>
        /// <param name="exportSettings">Export settings</param>
        /// <returns>Validation result</returns>
        ValidationResult ValidateExportSettings(string filePath, ExportSettings exportSettings);

        /// <summary>
        /// Gets the default export settings
        /// </summary>
        /// <returns>Default export settings</returns>
        ExportSettings GetDefaultExportSettings();

        /// <summary>
        /// Event raised when export progress changes
        /// </summary>
        event EventHandler<ExportProgressEventArgs> ProgressChanged;

        /// <summary>
        /// Event raised when export status changes
        /// </summary>
        event EventHandler<ExportStatusEventArgs> StatusChanged;
    }

    /// <summary>
    /// Configuration settings for Excel export
    /// </summary>
    public class ExportSettings
    {
        /// <summary>
        /// Include fire stopping element details
        /// </summary>
        public bool IncludeFireStoppingDetails { get; set; } = true;

        /// <summary>
        /// Include connected service details
        /// </summary>
        public bool IncludeServiceDetails { get; set; } = true;

        /// <summary>
        /// Include adjacent structure details
        /// </summary>
        public bool IncludeStructureDetails { get; set; } = true;

        /// <summary>
        /// Include design check results
        /// </summary>
        public bool IncludeDesignChecks { get; set; } = true;

        /// <summary>
        /// Include only elements with failures
        /// </summary>
        public bool FailuresOnly { get; set; } = false;

        /// <summary>
        /// Include metadata sheet
        /// </summary>
        public bool IncludeMetadata { get; set; } = true;

        /// <summary>
        /// Include summary sheet
        /// </summary>
        public bool IncludeSummary { get; set; } = true;

        /// <summary>
        /// Apply formatting and styling
        /// </summary>
        public bool ApplyFormatting { get; set; } = true;

        /// <summary>
        /// Freeze header rows
        /// </summary>
        public bool FreezeHeaders { get; set; } = true;

        /// <summary>
        /// Auto-fit column widths
        /// </summary>
        public bool AutoFitColumns { get; set; } = true;

        /// <summary>
        /// Add filters to data tables
        /// </summary>
        public bool AddFilters { get; set; } = true;

        /// <summary>
        /// Project name for metadata
        /// </summary>
        public string ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// User name for metadata
        /// </summary>
        public string UserName { get; set; } = Environment.UserName;

        /// <summary>
        /// Additional custom columns to include
        /// </summary>
        public Dictionary<string, string> CustomColumns { get; set; } = new();
    }

    /// <summary>
    /// Result of an Excel export operation
    /// </summary>
    public class ExportResult
    {
        /// <summary>
        /// Indicates if the export was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Path to the exported file
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// Number of elements exported
        /// </summary>
        public int ElementsExported { get; set; }

        /// <summary>
        /// Number of sheets created
        /// </summary>
        public int SheetsCreated { get; set; }

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSizeBytes { get; set; }

        /// <summary>
        /// Time taken for export
        /// </summary>
        public TimeSpan ExportDuration { get; set; }

        /// <summary>
        /// Any warnings encountered during export
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Error message if export failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Exception details if export failed
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// Timestamp when export was completed
        /// </summary>
        public DateTime CompletedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Preview information for Excel export
    /// </summary>
    public class ExportPreview
    {
        /// <summary>
        /// Number of elements to be exported
        /// </summary>
        public int TotalElements { get; set; }

        /// <summary>
        /// Number of elements with failures
        /// </summary>
        public int ElementsWithFailures { get; set; }

        /// <summary>
        /// Estimated number of rows
        /// </summary>
        public int EstimatedRows { get; set; }

        /// <summary>
        /// Estimated number of columns
        /// </summary>
        public int EstimatedColumns { get; set; }

        /// <summary>
        /// List of sheets that will be created
        /// </summary>
        public List<string> SheetNames { get; set; } = new();

        /// <summary>
        /// Estimated file size in KB
        /// </summary>
        public int EstimatedFileSizeKB { get; set; }

        /// <summary>
        /// Column groups that will be included
        /// </summary>
        public List<string> ColumnGroups { get; set; } = new();
    }

    /// <summary>
    /// Event arguments for export progress updates
    /// </summary>
    public class ExportProgressEventArgs : EventArgs
    {
        public int Current { get; set; }
        public int Total { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public string SheetName { get; set; } = string.Empty;
        public double PercentComplete => Total > 0 ? (double)Current / Total * 100 : 0;
    }

    /// <summary>
    /// Event arguments for export status updates
    /// </summary>
    public class ExportStatusEventArgs : EventArgs
    {
        public string Status { get; set; } = string.Empty;
        public bool IsError { get; set; }
        public Exception? Exception { get; set; }
        public string Operation { get; set; } = string.Empty;
    }
}
