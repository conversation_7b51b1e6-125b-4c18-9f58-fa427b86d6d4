using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.Pacifire.Models
{
    /// <summary>
    /// Represents filter settings for the fire stopping analysis.
    /// Contains selections for levels, categories, and linked models.
    /// </summary>
    public partial class FilterSettings : ObservableObject
    {
        /// <summary>
        /// Selected levels for filtering
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<LevelFilter> _selectedLevels = new();

        /// <summary>
        /// Selected categories for filtering
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CategoryFilter> _selectedCategories = new();

        /// <summary>
        /// Selected linked models for filtering
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<LinkedModelFilter> _selectedLinkedModels = new();

        /// <summary>
        /// Include only elements with design check failures
        /// </summary>
        [ObservableProperty]
        private bool _showFailuresOnly = false;

        /// <summary>
        /// Include elements without connected services
        /// </summary>
        [ObservableProperty]
        private bool _includeUnconnectedServices = true;

        /// <summary>
        /// Include elements without adjacent structures
        /// </summary>
        [ObservableProperty]
        private bool _includeUnconnectedStructures = true;

        /// <summary>
        /// Minimum distance threshold for adjacency checks (in millimeters)
        /// </summary>
        [ObservableProperty]
        private double _adjacencyThreshold = 300.0;

        /// <summary>
        /// Search text for filtering elements
        /// </summary>
        [ObservableProperty]
        private string _searchText = string.Empty;

        /// <summary>
        /// Timestamp when filters were last applied
        /// </summary>
        [ObservableProperty]
        private DateTime _lastApplied = DateTime.Now;

        /// <summary>
        /// Constructor for creating new filter settings
        /// </summary>
        public FilterSettings()
        {
            // Subscribe to collection changes to update FilterSummary
            SelectedLevels.CollectionChanged += OnFilterCollectionChanged;
            SelectedCategories.CollectionChanged += OnFilterCollectionChanged;
            SelectedLinkedModels.CollectionChanged += OnFilterCollectionChanged;

            // Initialize with default selections
            InitializeDefaultFilters();

            // Subscribe to existing items
            SubscribeToExistingItems();
        }

        /// <summary>
        /// Handles collection changes for filter collections
        /// </summary>
        private void OnFilterCollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // Unsubscribe from old items
            if (e.OldItems != null)
            {
                foreach (var item in e.OldItems.OfType<System.ComponentModel.INotifyPropertyChanged>())
                {
                    item.PropertyChanged -= OnFilterItemPropertyChanged;
                }
            }

            // Subscribe to new items
            if (e.NewItems != null)
            {
                foreach (var item in e.NewItems.OfType<System.ComponentModel.INotifyPropertyChanged>())
                {
                    item.PropertyChanged += OnFilterItemPropertyChanged;
                }
            }

            OnPropertyChanged(nameof(FilterSummary));
        }

        /// <summary>
        /// Handles property changes of individual filter items
        /// </summary>
        private void OnFilterItemPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(LevelFilter.IsSelected) ||
                e.PropertyName == nameof(CategoryFilter.IsSelected) ||
                e.PropertyName == nameof(LinkedModelFilter.IsSelected))
            {
                OnPropertyChanged(nameof(FilterSummary));
            }
        }

        /// <summary>
        /// Subscribes to PropertyChanged events of existing filter items
        /// </summary>
        private void SubscribeToExistingItems()
        {
            foreach (var item in SelectedLevels)
                item.PropertyChanged += OnFilterItemPropertyChanged;

            foreach (var item in SelectedCategories)
                item.PropertyChanged += OnFilterItemPropertyChanged;

            foreach (var item in SelectedLinkedModels)
                item.PropertyChanged += OnFilterItemPropertyChanged;
        }

        /// <summary>
        /// Called when ShowFailuresOnly property changes
        /// </summary>
        partial void OnShowFailuresOnlyChanged(bool value)
        {
            OnPropertyChanged(nameof(FilterSummary));
        }

        /// <summary>
        /// Called when SearchText property changes
        /// </summary>
        partial void OnSearchTextChanged(string value)
        {
            OnPropertyChanged(nameof(FilterSummary));
        }

        /// <summary>
        /// Indicates if any filters are active
        /// </summary>
        public bool HasActiveFilters =>
            SelectedLevels.Any(x => x.IsSelected) ||
            SelectedCategories.Any(x => x.IsSelected) ||
            SelectedLinkedModels.Any(x => x.IsSelected) ||
            ShowFailuresOnly ||
            !string.IsNullOrEmpty(SearchText);

        /// <summary>
        /// Gets a summary of active filters
        /// </summary>
        public string FilterSummary
        {
            get
            {
                var summary = new List<string>();

                var selectedLevelsCount = SelectedLevels.Count(x => x.IsSelected);
                if (selectedLevelsCount > 0)
                    summary.Add($"{selectedLevelsCount} Level(s)");

                var selectedCategoriesCount = SelectedCategories.Count(x => x.IsSelected);
                if (selectedCategoriesCount > 0)
                    summary.Add($"{selectedCategoriesCount} Category(ies)");

                var selectedLinkedModelsCount = SelectedLinkedModels.Count(x => x.IsSelected);
                if (selectedLinkedModelsCount > 0)
                    summary.Add($"{selectedLinkedModelsCount} Linked Model(s)");

                if (ShowFailuresOnly)
                    summary.Add("Failures Only");

                if (!string.IsNullOrEmpty(SearchText))
                    summary.Add($"Search: '{SearchText}'");

                return summary.Count > 0 ? string.Join(", ", summary) : "No Filters";
            }
        }

        /// <summary>
        /// Initializes default filter selections
        /// </summary>
        private void InitializeDefaultFilters()
        {
            // Default categories
            SelectedCategories.Add(new CategoryFilter("Duct Fittings", true));
            SelectedCategories.Add(new CategoryFilter("Pipe Fittings", true));
            SelectedCategories.Add(new CategoryFilter("Cable Tray Fittings", true));
            SelectedCategories.Add(new CategoryFilter("Conduit Fittings", true));
        }

        /// <summary>
        /// Resets all filters to default state
        /// </summary>
        public void Reset()
        {
            SelectedLevels.Clear();
            SelectedCategories.Clear();
            SelectedLinkedModels.Clear();
            ShowFailuresOnly = false;
            IncludeUnconnectedServices = true;
            IncludeUnconnectedStructures = true;
            AdjacencyThreshold = 300.0;
            SearchText = string.Empty;
            InitializeDefaultFilters();
            LastApplied = DateTime.Now;
        }

        /// <summary>
        /// Creates a copy of these filter settings
        /// </summary>
        /// <returns>A new FilterSettings with copied values</returns>
        public FilterSettings Clone()
        {
            var clone = new FilterSettings
            {
                ShowFailuresOnly = ShowFailuresOnly,
                IncludeUnconnectedServices = IncludeUnconnectedServices,
                IncludeUnconnectedStructures = IncludeUnconnectedStructures,
                AdjacencyThreshold = AdjacencyThreshold,
                SearchText = SearchText,
                LastApplied = LastApplied
            };

            // Copy collections
            clone.SelectedLevels.Clear();
            foreach (var level in SelectedLevels)
                clone.SelectedLevels.Add(level.Clone());

            clone.SelectedCategories.Clear();
            foreach (var category in SelectedCategories)
                clone.SelectedCategories.Add(category.Clone());

            clone.SelectedLinkedModels.Clear();
            foreach (var model in SelectedLinkedModels)
                clone.SelectedLinkedModels.Add(model.Clone());

            return clone;
        }
    }

    /// <summary>
    /// Represents a level filter item
    /// </summary>
    public partial class LevelFilter : ObservableObject
    {
        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private bool _isSelected;

        [ObservableProperty]
        private double _elevation;

        public LevelFilter() { }

        public LevelFilter(string name, bool isSelected = false, double elevation = 0)
        {
            Name = name;
            IsSelected = isSelected;
            Elevation = elevation;
        }

        public LevelFilter Clone() => new(Name, IsSelected, Elevation);
    }

    /// <summary>
    /// Represents a category filter item
    /// </summary>
    public partial class CategoryFilter : ObservableObject
    {
        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private bool _isSelected;

        [ObservableProperty]
        private string _description = string.Empty;

        public CategoryFilter() { }

        public CategoryFilter(string name, bool isSelected = false, string description = "")
        {
            Name = name;
            IsSelected = isSelected;
            Description = description;
        }

        public CategoryFilter Clone() => new(Name, IsSelected, Description);
    }

    /// <summary>
    /// Represents a linked model filter item
    /// </summary>
    public partial class LinkedModelFilter : ObservableObject
    {
        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private bool _isSelected;

        [ObservableProperty]
        private string _filePath = string.Empty;

        [ObservableProperty]
        private string _modelType = string.Empty; // MEP, Architectural, Structural, etc.

        public LinkedModelFilter() { }

        public LinkedModelFilter(string name, bool isSelected = false, string filePath = "", string modelType = "")
        {
            Name = name;
            IsSelected = isSelected;
            FilePath = filePath;
            ModelType = modelType;
        }

        public LinkedModelFilter Clone() => new(Name, IsSelected, FilePath, ModelType);
    }
}
