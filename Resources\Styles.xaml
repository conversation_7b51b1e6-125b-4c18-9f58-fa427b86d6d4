<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Color Resources -->
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
    <SolidColorBrush x:Key="LightGrayBrush" Color="#F5F5F5"/>
    <SolidColorBrush x:Key="MediumGrayBrush" Color="#E0E0E0"/>
    <SolidColorBrush x:Key="DarkGrayBrush" Color="#757575"/>

    <!-- Button Styles -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidForegroundBrush}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinWidth" Value="120"/>
    </Style>

    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- Card Styles -->
    <Style x:Key="SummaryCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
    </Style>

    <Style x:Key="SuccessCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource SummaryCardStyle}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="ErrorCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource SummaryCardStyle}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style x:Key="WarningCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource SummaryCardStyle}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- Text Styles -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
    </Style>

    <Style x:Key="SuccessTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyTextStyle}">
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <Style x:Key="ErrorTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyTextStyle}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <Style x:Key="WarningTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyTextStyle}">
        <Setter Property="Foreground" Value="{StaticResource WarningBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- DataGrid Styles -->
    <Style x:Key="MainDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="materialDesign:DataGridAssist.CellPadding" Value="8"/>
        <Setter Property="materialDesign:DataGridAssist.ColumnHeaderPadding" Value="8"/>
        <Setter Property="RowHeight" Value="40"/>
        <Setter Property="ColumnHeaderHeight" Value="48"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <!-- Progress Bar Styles -->
    <Style x:Key="MainProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="6"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Background" Value="{StaticResource LightGrayBrush}"/>
    </Style>

    <!-- Sidebar Styles -->
    <Style x:Key="SidebarStyle" TargetType="materialDesign:Card">
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="Margin" Value="8"/>
    </Style>

    <Style x:Key="SidebarButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="0,2"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <Style x:Key="SidebarIconButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
        <Setter Property="Width" Value="48"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <Style x:Key="SidebarGroupHeaderStyle" TargetType="TextBlock">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,16,0,8"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        <!--<Setter Property="TextTransform" Value="Upper"/>-->
    </Style>

    <!-- CheckBox Styles -->
    <Style x:Key="FilterCheckBoxStyle" TargetType="CheckBox" BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <!-- TextBox Styles -->
    <Style x:Key="SearchTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="materialDesign:HintAssist.Hint" Value="Search elements..."/>
        <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
    </Style>

    <!-- Status Bar Styles -->
    <Style x:Key="StatusBarStyle" TargetType="materialDesign:ColorZone">
        <Setter Property="Mode" Value="PrimaryLight"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
    </Style>

    <!-- Icon Styles -->
    <Style x:Key="StatusIconStyle" TargetType="materialDesign:PackIcon">
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <Style x:Key="LargeIconStyle" TargetType="materialDesign:PackIcon">
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Animation Styles -->
    <Style x:Key="FadeInStyle" TargetType="FrameworkElement">
        <Setter Property="Opacity" Value="0"/>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity" 
                                       From="0" To="1" Duration="0:0:0.3"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- Tooltip Styles -->
    <Style x:Key="CustomToolTipStyle" TargetType="ToolTip">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="HasDropShadow" Value="True"/>
    </Style>

</ResourceDictionary>
