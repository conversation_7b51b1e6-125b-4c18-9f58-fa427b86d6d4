{
  "project": "Passive Fire Stopping Schedule Exporter",
  "description": "Revit add-in that extracts fire stopping fittings from MEP links, identifies their connected services and adjacent structural walls/floors from other linked models, validates their spatial relationships, and exports a structured schedule.",
  "goals": [
    "Detect broken fire stopping placements across multiple linked models",
    "Extract specific parameters from Fire Stopping, Services, and Structure elements",
    "Perform spatial design checks",
    "Display results in a grouped WPF UI with filtering",
    "Export to Excel with grouped sections and metadata"
  ],
  "architecture": {
    "framework": ".NET 6+",
    "revitContext": true,
    "linkedFileSupport": true,
    "patterns": ["MVVM", "Dependency Injection"],
    "libraries": ["CommunityToolkit.Mvvm", "MaterialDesignThemes", "ClosedXML"],
    "testability": true,
    "extensibility": true
  },
  "components": {
    "fireStoppingElements": {
      "source": "Fittings/Accessories from linked Ducts, Pipes, Cable Trays",
      "filter": "Family type name contains 'Fire Stopping'",
      "parameters": [
        "ElementId",
        "Beca Type Mark",
        "Beca Inst Mark",
        "Beca System Description",
        "Beca Family Material",
        "Beca Free Size",
        "Beca Family Orientation",
        "Family Name",
        "Type Name",
        "Beca Family Reference"
      ]
    },
    "connectedServices": {
      "source": "Directly connected to Fire Stopping fittings",
      "parameters": [
        "Service Type",
        "Size",
        "Material",
        "System Type"
      ]
    },
    "adjacentStructures": {
      "source": "Walls and Floors from linked architectural model",
      "parameters": [
        "Structure Type",
        "Material Type",
        "Fire Rating",
        "Thickness"
      ]
    }
  },
  "filters": {
    "levels": "Multi-select",
    "categories": "Multi-select (e.g., Fire Stopping, Pipes, Ducts)"
    "linkedModels": "Multi-select from all currently loaded linked models in the host project"
  },
  "designChecks": [
    {
    "name": "NotTouchingWall",
    "description": "Fitting does not intersect any wall or floor from linked structure models"
  },
  {
    "name": "NotTouchingService",
    "description": "Fitting has no connector or bounding box contact with valid MEP system"
  },
  {
    "name": "Clashing",
    "description": "Fitting intersects another fire stopping element"
  },
  {
    "name": "Adjacent",
    "description": "Fitting is within 300mm of another fire stopping element"
  }
  ],
  "intersectionLogic": {
  "critical": true,
  "description": "The accuracy and performance of the tool depend heavily on this logic.",
  "requirements": [
    "All geometry comparisons must be done in host coordinate space.",
    "Linked element geometry must be transformed using RevitLinkInstance.GetTransform().",
    "Use bounding box filtering to reduce solid intersection computations.",
    "For 'Clashing' and 'Adjacent' checks, use proximity filtering or spatial partitioning.",
    "Cache transformed geometry where possible.",
    "Only elements from selected linked models should be checked."
  ],
  "performanceNotes": [
    "This is the most computationally expensive part of the tool.",
    "Must avoid brute-force solid comparisons over all elements.",
    "Scalability is critical for large federated hospital projects."
  ]
},
  "ui": {
    "tech": "WPF with MaterialDesignThemes",
    "sidebar": {
      "type": "Collapsible",
      "toggleIcon": "☰",
      "collapsedState": "Icon-only with tooltips",
      "expandedState": "Full labels and controls",
      "sections": [
        { "label": "Levels", "control": "Multi-select checkboxes" },
        { "label": "Categories", "control": "Multi-select checkboxes" },
        { "label": "Linked Models", "control": "Multi-select checkboxes"},
        { "label": "Run", "control": "Button to extract + validate" }
      ]
    },
    "views": ["MainView.xaml with sidebar and grouped DataGrid"],
    "columnGroups": [
      "Fire Stopping Info",
      "Service Info",
      "Structure Info",
      "Design Check Results"
    ]
  },
  "output": {
    "format": "Excel",
    "groupedSections": [
      "Fire Stopping Parameters",
      "Connected Service Parameters",
      "Structure Parameters",
      "Check Flags"
    ],
    "metadata": ["Project name", "Datetime", "Filters", "User/machine name"]
  },
  "futureEnhancements": [
    "BIM 360 issue creation",
    "Zoom-to-element in Revit",
    "3D thumbnails",
    "Custom rules",
    "Native Revit schedule export"
  ]
}

