using System;
using System.Collections.Generic;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using Autodesk.Revit.DB;

namespace MEP.Pacifire.Models
{
    /// <summary>
    /// Represents the results of design checks performed on a fire stopping element.
    /// Contains boolean flags for each check type and detailed failure information.
    /// </summary>
    public partial class DesignCheckResult : ObservableObject
    {
        /// <summary>
        /// Indicates if the fire stopping element is not touching any wall or floor
        /// </summary>
        [ObservableProperty]
        private bool _notTouchingWall;

        /// <summary>
        /// Indicates if the fire stopping element is not connected to any service element
        /// </summary>
        [ObservableProperty]
        private bool _notTouchingService;

        /// <summary>
        /// Indicates if the fire stopping element is clashing (intersecting) with another fire stopping element
        /// </summary>
        [ObservableProperty]
        private bool _clashing;

        /// <summary>
        /// Indicates if the fire stopping element is within 300mm of another fire stopping element
        /// </summary>
        [ObservableProperty]
        private bool _adjacent;

        /// <summary>
        /// Detailed messages for each check failure
        /// </summary>
        [ObservableProperty]
        private Dictionary<string, string> _checkMessages = new();

        /// <summary>
        /// List of element IDs that this element is clashing with
        /// </summary>
        [ObservableProperty]
        private List<ElementId> _clashingElements = new();

        /// <summary>
        /// List of element IDs that this element is adjacent to
        /// </summary>
        [ObservableProperty]
        private List<ElementId> _adjacentElements = new();

        /// <summary>
        /// Distance to the nearest wall/floor (in millimeters)
        /// </summary>
        [ObservableProperty]
        private double _distanceToNearestWall = double.MaxValue;

        /// <summary>
        /// Distance to the nearest service element (in millimeters)
        /// </summary>
        [ObservableProperty]
        private double _distanceToNearestService = double.MaxValue;

        /// <summary>
        /// Distance to the nearest fire stopping element (in millimeters)
        /// </summary>
        [ObservableProperty]
        private double _distanceToNearestFireStopping = double.MaxValue;

        /// <summary>
        /// Timestamp when the checks were performed
        /// </summary>
        [ObservableProperty]
        private DateTime _checkedAt = DateTime.Now;

        /// <summary>
        /// Additional custom check results that may be added in the future
        /// </summary>
        [ObservableProperty]
        private Dictionary<string, bool> _customChecks = new();

        /// <summary>
        /// Constructor for creating a new DesignCheckResult
        /// </summary>
        public DesignCheckResult()
        {
            // Initialize with no failures
            NotTouchingWall = false;
            NotTouchingService = false;
            Clashing = false;
            Adjacent = false;
        }

        /// <summary>
        /// Indicates if any design checks have failed
        /// </summary>
        public bool HasFailures => NotTouchingWall || NotTouchingService || Clashing || Adjacent || CustomChecks.Values.Any(x => x);

        /// <summary>
        /// Gets the total number of failed checks
        /// </summary>
        public int FailureCount
        {
            get
            {
                int count = 0;
                if (NotTouchingWall) count++;
                if (NotTouchingService) count++;
                if (Clashing) count++;
                if (Adjacent) count++;
                count += CustomChecks.Values.Count(x => x);
                return count;
            }
        }

        /// <summary>
        /// Gets a summary of all failed checks
        /// </summary>
        public string FailureSummary
        {
            get
            {
                var failures = new List<string>();

                if (NotTouchingWall)
                    failures.Add("Not Touching Wall");
                if (NotTouchingService)
                    failures.Add("Not Touching Service");
                if (Clashing)
                    failures.Add("Clashing");
                if (Adjacent)
                    failures.Add("Adjacent");

                // Add custom check failures
                foreach (var customCheck in CustomChecks.Where(x => x.Value))
                {
                    failures.Add(customCheck.Key);
                }

                return failures.Count > 0 ? string.Join(", ", failures) : "No Failures";
            }
        }

        /// <summary>
        /// Gets a detailed report of all check results
        /// </summary>
        public string DetailedReport
        {
            get
            {
                var report = new List<string>();

                report.Add($"Design Check Results (Checked: {CheckedAt:yyyy-MM-dd HH:mm:ss})");
                report.Add("=" + new string('=', 50));

                // Standard checks
                report.Add($"Not Touching Wall: {(NotTouchingWall ? "FAIL" : "PASS")}");
                if (NotTouchingWall && CheckMessages.ContainsKey("NotTouchingWall"))
                    report.Add($"  → {CheckMessages["NotTouchingWall"]}");

                report.Add($"Not Touching Service: {(NotTouchingService ? "FAIL" : "PASS")}");
                if (NotTouchingService && CheckMessages.ContainsKey("NotTouchingService"))
                    report.Add($"  → {CheckMessages["NotTouchingService"]}");

                report.Add($"Clashing: {(Clashing ? "FAIL" : "PASS")}");
                if (Clashing && ClashingElements.Count > 0)
                    report.Add($"  → Clashing with {ClashingElements.Count} element(s)");

                report.Add($"Adjacent: {(Adjacent ? "FAIL" : "PASS")}");
                if (Adjacent && AdjacentElements.Count > 0)
                    report.Add($"  → Adjacent to {AdjacentElements.Count} element(s)");

                // Custom checks
                if (CustomChecks.Count > 0)
                {
                    report.Add("");
                    report.Add("Custom Checks:");
                    foreach (var customCheck in CustomChecks)
                    {
                        report.Add($"{customCheck.Key}: {(customCheck.Value ? "FAIL" : "PASS")}");
                        if (customCheck.Value && CheckMessages.ContainsKey(customCheck.Key))
                            report.Add($"  → {CheckMessages[customCheck.Key]}");
                    }
                }

                // Distance information
                report.Add("");
                report.Add("Distance Information:");
                if (DistanceToNearestWall < double.MaxValue)
                    report.Add($"Distance to Nearest Wall: {DistanceToNearestWall:F1}mm");
                if (DistanceToNearestService < double.MaxValue)
                    report.Add($"Distance to Nearest Service: {DistanceToNearestService:F1}mm");
                if (DistanceToNearestFireStopping < double.MaxValue)
                    report.Add($"Distance to Nearest Fire Stopping: {DistanceToNearestFireStopping:F1}mm");

                return string.Join(Environment.NewLine, report);
            }
        }

        /// <summary>
        /// Sets a check message for a specific check type
        /// </summary>
        /// <param name="checkType">Type of check</param>
        /// <param name="message">Detailed message</param>
        public void SetCheckMessage(string checkType, string message)
        {
            CheckMessages[checkType] = message;
        }

        /// <summary>
        /// Gets a check message for a specific check type
        /// </summary>
        /// <param name="checkType">Type of check</param>
        /// <returns>Message or empty string if not found</returns>
        public string GetCheckMessage(string checkType)
        {
            return CheckMessages.TryGetValue(checkType, out var message) ? message : string.Empty;
        }

        /// <summary>
        /// Adds a custom check result
        /// </summary>
        /// <param name="checkName">Name of the custom check</param>
        /// <param name="failed">Whether the check failed</param>
        /// <param name="message">Optional message</param>
        public void AddCustomCheck(string checkName, bool failed, string message = "")
        {
            CustomChecks[checkName] = failed;
            if (!string.IsNullOrEmpty(message))
            {
                SetCheckMessage(checkName, message);
            }
        }

        /// <summary>
        /// Resets all check results to passing state
        /// </summary>
        public void Reset()
        {
            NotTouchingWall = false;
            NotTouchingService = false;
            Clashing = false;
            Adjacent = false;
            CheckMessages.Clear();
            ClashingElements.Clear();
            AdjacentElements.Clear();
            CustomChecks.Clear();
            DistanceToNearestWall = double.MaxValue;
            DistanceToNearestService = double.MaxValue;
            DistanceToNearestFireStopping = double.MaxValue;
            CheckedAt = DateTime.Now;
        }

        /// <summary>
        /// Creates a copy of this design check result
        /// </summary>
        /// <returns>A new DesignCheckResult with copied values</returns>
        public DesignCheckResult Clone()
        {
            return new DesignCheckResult
            {
                NotTouchingWall = NotTouchingWall,
                NotTouchingService = NotTouchingService,
                Clashing = Clashing,
                Adjacent = Adjacent,
                CheckMessages = new Dictionary<string, string>(CheckMessages),
                ClashingElements = new List<ElementId>(ClashingElements),
                AdjacentElements = new List<ElementId>(AdjacentElements),
                DistanceToNearestWall = DistanceToNearestWall,
                DistanceToNearestService = DistanceToNearestService,
                DistanceToNearestFireStopping = DistanceToNearestFireStopping,
                CustomChecks = new Dictionary<string, bool>(CustomChecks),
                CheckedAt = CheckedAt
            };
        }
    }
}
