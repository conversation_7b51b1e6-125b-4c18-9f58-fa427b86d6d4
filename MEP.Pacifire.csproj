﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<UseWPF>true</UseWPF>
		<UseWindowsForms>true</UseWindowsForms>
		<Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Release R20;Release R21;Release R22;Release R23;Release R24;Release R25;Debug R26;Release R26</Configurations>
	</PropertyGroup>
	<ItemGroup>
	  <Compile Remove="Tests\**" />
	  <EmbeddedResource Remove="Tests\**" />
	  <None Remove="Tests\**" />
	  <Page Remove="Tests\**" />
	</ItemGroup>
	<ItemGroup>
	  <None Remove="Resources\BecaLogoBlack.png" />
	  <None Remove="Views\BecaLogoBlack.png" />
	</ItemGroup>
  
	<ItemGroup>
		<PackageReference Include="ClosedXML" Version="0.104.1" />
		<PackageReference Include="MahApps.Metro" Version="2.4.10" />
		<PackageReference Include="MaterialDesignThemes.MahApps" Version="0.3.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Nice3point.Revit.Build.Tasks" Version="2.0.2" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPI" Version="$(RevitVersion).*" />
		<PackageReference Include="Nice3point.Revit.Api.RevitAPIUI" Version="$(RevitVersion).*" />
		<PackageReference Include="WPF-UI" Version="3.0.5" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\COMMON\BecaActivityLogger\BecaActivityLogger.csproj" />
	  <ProjectReference Include="..\COMMON\BecaCommand\BecaCommand.csproj" />
	  <ProjectReference Include="..\COMMON\BecaRevitUtilities\BecaRevitUtilities.csproj" />
	  <ProjectReference Include="..\COMMON\BecaTransactionsNames\BecaTransactionsNamesManager.csproj" />
	  <ProjectReference Include="..\COMMON\Common.UI.WPF\Common.UI.WPF.csproj" />
	  <ProjectReference Include="..\COMMON\Common.UI\Common.UI.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Resource Include="Resources\BecaLogoBlack.png" />
	  <Resource Include="Views\BecaLogoBlack.png" />
	</ItemGroup>
	<ItemGroup>
		<None Update="Properties\Settings.settings">
			<Generator>SettingsSingleFileGenerator</Generator>
			<LastGenOutput>Settings.Designer.cs</LastGenOutput>
		</None>
	</ItemGroup>

</Project>
