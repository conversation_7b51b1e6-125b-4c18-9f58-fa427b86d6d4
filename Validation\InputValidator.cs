using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using MEP.Pacifire.Services;

namespace MEP.Pacifire.Validation
{
    /// <summary>
    /// Comprehensive input validation for the Fire Stopping Solution Exporter.
    /// Provides validation for user inputs, file paths, and system requirements.
    /// </summary>
    public static class InputValidator
    {
        private static readonly Regex FileNameRegex = new(@"^[^<>:""/\\|?*]+$", RegexOptions.Compiled);
        private static readonly Regex SearchTextRegex = new(@"^[a-zA-Z0-9\s\-_\.]*$", RegexOptions.Compiled);

        /// <summary>
        /// Validates export file path and settings
        /// </summary>
        /// <param name="filePath">File path to validate</param>
        /// <param name="exportSettings">Export settings to validate</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateExportPath(string filePath, ExportSettings? exportSettings = null)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // Check if path is provided
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    result.IsValid = false;
                    result.Issues.Add("File path cannot be empty.");
                    return result;
                }

                // Validate path format
                if (!IsValidFilePath(filePath))
                {
                    result.IsValid = false;
                    result.Issues.Add("File path contains invalid characters.");
                    return result;
                }

                // Check directory exists
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    result.IsValid = false;
                    result.Issues.Add($"Directory does not exist: {directory}");
                    return result;
                }

                // Check file extension
                var extension = Path.GetExtension(filePath);
                if (!extension.Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    result.Warnings.Add("Recommended file extension is .xlsx for Excel compatibility.");
                }

                // Check if file already exists
                if (File.Exists(filePath))
                {
                    result.Warnings.Add("File already exists and will be overwritten.");
                    
                    // Check if file is read-only
                    var fileInfo = new FileInfo(filePath);
                    if (fileInfo.IsReadOnly)
                    {
                        result.IsValid = false;
                        result.Issues.Add("Cannot overwrite read-only file.");
                        return result;
                    }
                }

                // Check available disk space
                var drive = new DriveInfo(Path.GetPathRoot(filePath) ?? "C:");
                if (drive.AvailableFreeSpace < 10 * 1024 * 1024) // 10MB minimum
                {
                    result.IsValid = false;
                    result.Issues.Add("Insufficient disk space for export.");
                }

                // Validate export settings if provided
                if (exportSettings != null)
                {
                    ValidateExportSettings(exportSettings, result);
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Issues.Add($"Error validating file path: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Validates filter settings for analysis
        /// </summary>
        /// <param name="filterSettings">Filter settings to validate</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateFilterSettings(FilterSettings filterSettings)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                if (filterSettings == null)
                {
                    result.IsValid = false;
                    result.Issues.Add("Filter settings cannot be null.");
                    return result;
                }

                // Validate adjacency threshold
                if (filterSettings.AdjacencyThreshold <= 0 || filterSettings.AdjacencyThreshold > 10000)
                {
                    result.IsValid = false;
                    result.Issues.Add("Adjacency threshold must be between 1 and 10000 millimeters.");
                }

                // Validate search text
                if (!string.IsNullOrEmpty(filterSettings.SearchText))
                {
                    if (filterSettings.SearchText.Length > 100)
                    {
                        result.Warnings.Add("Search text is very long and may affect performance.");
                    }

                    if (!SearchTextRegex.IsMatch(filterSettings.SearchText))
                    {
                        result.Warnings.Add("Search text contains special characters that may not match as expected.");
                    }
                }

                // Check if any filters are selected
                var hasLevelFilter = filterSettings.SelectedLevels?.Any(x => x.IsSelected) == true;
                var hasCategoryFilter = filterSettings.SelectedCategories?.Any(x => x.IsSelected) == true;
                var hasModelFilter = filterSettings.SelectedLinkedModels?.Any(x => x.IsSelected) == true;

                if (!hasLevelFilter && !hasCategoryFilter && !hasModelFilter && !filterSettings.ShowFailuresOnly)
                {
                    result.Warnings.Add("No filters are active. All elements will be processed.");
                }

                // Validate linked model selections
                if (filterSettings.SelectedLinkedModels != null)
                {
                    var selectedModels = filterSettings.SelectedLinkedModels.Where(x => x.IsSelected).ToList();
                    if (selectedModels.Count > 10)
                    {
                        result.Warnings.Add("Many linked models selected. This may impact performance.");
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Issues.Add($"Error validating filter settings: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Validates Revit document for analysis requirements
        /// </summary>
        /// <param name="document">Revit document to validate</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateDocument(Document document)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                if (document == null)
                {
                    result.IsValid = false;
                    result.Issues.Add("Document cannot be null.");
                    return result;
                }

                // Check if it's a family document
                if (document.IsFamilyDocument)
                {
                    result.IsValid = false;
                    result.Issues.Add("Family documents are not supported. Please open a project document.");
                    return result;
                }

                // Check if document is read-only
                if (document.IsReadOnly)
                {
                    result.Warnings.Add("Document is read-only. Some features may be limited.");
                }

                // Check for linked models
                var linkInstances = new FilteredElementCollector(document)
                    .OfClass(typeof(RevitLinkInstance))
                    .Cast<RevitLinkInstance>()
                    .ToList();

                if (!linkInstances.Any())
                {
                    result.Warnings.Add("No linked models found. Analysis will be limited to host model elements.");
                }
                else
                {
                    var loadedLinks = linkInstances.Where(link => link.GetLinkDocument() != null).ToList();
                    var unloadedLinks = linkInstances.Count - loadedLinks.Count;

                    if (unloadedLinks > 0)
                    {
                        result.Warnings.Add($"{unloadedLinks} linked model(s) are not loaded and will be skipped.");
                    }

                    if (loadedLinks.Count == 0)
                    {
                        result.Warnings.Add("No linked models are loaded. Analysis will be limited to host model elements.");
                    }
                }

                // Check for levels
                var levels = new FilteredElementCollector(document)
                    .OfClass(typeof(Level))
                    .Cast<Level>()
                    .ToList();

                if (!levels.Any())
                {
                    result.Warnings.Add("No levels found in the document.");
                }

                // Check for fire stopping categories
                var fireStoppingElements = GetFireStoppingElements(document);
                if (!fireStoppingElements.Any())
                {
                    result.Warnings.Add("No fire stopping elements found in the document or linked models.");
                }

                // Check document units
                var units = document.GetUnits();
                var lengthUnit = units.GetFormatOptions(SpecTypeId.Length);
                if (lengthUnit.GetUnitTypeId() != UnitTypeId.Millimeters && 
                    lengthUnit.GetUnitTypeId() != UnitTypeId.Feet)
                {
                    result.Warnings.Add("Document uses non-standard length units. Results may need interpretation.");
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Issues.Add($"Error validating document: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Validates system requirements for the application
        /// </summary>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateSystemRequirements()
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // Check .NET version
                var version = Environment.Version;
                if (version.Major < 6)
                {
                    result.IsValid = false;
                    result.Issues.Add($".NET version {version} is not supported. Requires .NET 6.0 or later.");
                }

                // Check available memory
                var totalMemory = GC.GetTotalMemory(false);
                if (totalMemory > 1024 * 1024 * 1024) // 1GB
                {
                    result.Warnings.Add("High memory usage detected. Consider restarting Revit if performance is slow.");
                }

                // Check processor count
                if (Environment.ProcessorCount < 2)
                {
                    result.Warnings.Add("Single-core processor detected. Multi-threading will be limited.");
                }

                // Check operating system
                var os = Environment.OSVersion;
                if (os.Platform != PlatformID.Win32NT)
                {
                    result.IsValid = false;
                    result.Issues.Add("Windows operating system is required.");
                }

                if (os.Version.Major < 10)
                {
                    result.Warnings.Add("Windows 10 or later is recommended for best performance.");
                }

                // Check application data directory access
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                if (!Directory.Exists(appDataPath))
                {
                    result.IsValid = false;
                    result.Issues.Add("Cannot access application data directory.");
                }
                else
                {
                    try
                    {
                        var testPath = Path.Combine(appDataPath, "MEP.Pacifire.Test");
                        Directory.CreateDirectory(testPath);
                        Directory.Delete(testPath);
                    }
                    catch
                    {
                        result.IsValid = false;
                        result.Issues.Add("Insufficient permissions to create application data directories.");
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Issues.Add($"Error validating system requirements: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Validates user input for custom parameters
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <param name="parameterValue">Parameter value</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateCustomParameter(string parameterName, object? parameterValue)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // Validate parameter name
                if (string.IsNullOrWhiteSpace(parameterName))
                {
                    result.IsValid = false;
                    result.Issues.Add("Parameter name cannot be empty.");
                    return result;
                }

                if (parameterName.Length > 100)
                {
                    result.IsValid = false;
                    result.Issues.Add("Parameter name is too long (maximum 100 characters).");
                    return result;
                }

                if (!Regex.IsMatch(parameterName, @"^[a-zA-Z][a-zA-Z0-9_\s]*$"))
                {
                    result.IsValid = false;
                    result.Issues.Add("Parameter name must start with a letter and contain only letters, numbers, underscores, and spaces.");
                    return result;
                }

                // Validate parameter value
                if (parameterValue != null)
                {
                    var valueString = parameterValue.ToString();
                    if (!string.IsNullOrEmpty(valueString) && valueString.Length > 1000)
                    {
                        result.Warnings.Add("Parameter value is very long and may affect performance.");
                    }
                }
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Issues.Add($"Error validating custom parameter: {ex.Message}");
            }

            return result;
        }

        #region Private Helper Methods

        private static bool IsValidFilePath(string path)
        {
            try
            {
                var fileName = Path.GetFileName(path);
                var directory = Path.GetDirectoryName(path);

                return !string.IsNullOrEmpty(fileName) &&
                       FileNameRegex.IsMatch(fileName) &&
                       (string.IsNullOrEmpty(directory) || Directory.Exists(Path.GetPathRoot(directory)));
            }
            catch
            {
                return false;
            }
        }

        private static void ValidateExportSettings(ExportSettings exportSettings, ValidationResult result)
        {
            if (!exportSettings.IncludeFireStoppingDetails &&
                !exportSettings.IncludeServiceDetails &&
                !exportSettings.IncludeStructureDetails &&
                !exportSettings.IncludeDesignChecks)
            {
                result.IsValid = false;
                result.Issues.Add("At least one data section must be included in the export.");
            }

            if (string.IsNullOrWhiteSpace(exportSettings.ProjectName))
            {
                result.Warnings.Add("Project name is empty. Default name will be used.");
            }

            if (string.IsNullOrWhiteSpace(exportSettings.UserName))
            {
                result.Warnings.Add("User name is empty. Default name will be used.");
            }
        }

        private static IEnumerable<Element> GetFireStoppingElements(Document document)
        {
            var categories = new[]
            {
                BuiltInCategory.OST_DuctFitting,
                BuiltInCategory.OST_PipeFitting,
                BuiltInCategory.OST_CableTrayFitting,
                BuiltInCategory.OST_ConduitFitting
            };

            var elements = new List<Element>();

            foreach (var category in categories)
            {
                try
                {
                    var categoryElements = new FilteredElementCollector(document)
                        .OfCategory(category)
                        .WhereElementIsNotElementType()
                        .ToElements();

                    elements.AddRange(categoryElements);
                }
                catch
                {
                    // Category might not exist in this document
                }
            }

            return elements;
        }

        #endregion
    }
}
