# Passive Fire Stopping Solution Exporter

A comprehensive Revit add-in for auditing and resolving placement issues of fire stopping elements across multiple linked models. Built for Passive Fire Engineers to identify incorrectly placed or broken fire stopping fittings, analyze their connection to services and structural barriers, perform spatial validation, and export results in a structured, engineer-friendly format.

## 🎯 Purpose

This Revit add-in is designed to help Passive Fire Engineers:
- Audit fire stopping elements across multiple linked models
- Identify placement issues and disconnections
- Perform spatial validation and design checks
- Export comprehensive analysis results to Excel

## 🚀 Features

### Core Functionality
- **Multi-Model Analysis**: Extract fire stopping fittings from MEP links (Ducts, Pipes, Cable Trays)
- **Service Detection**: Identify and extract connected service elements
- **Structural Analysis**: Locate adjacent wall/floor structures from architectural links
- **Spatial Validation**: Detect misalignments and disconnections with accurate coordinate transformation
- **Design Checks**: Perform four critical validation checks on each element
- **Excel Export**: Generate structured schedules with critical metadata using ClosedXML

### Design Checks
1. **NotTouchingWall**: Fire stopping element is not adjacent to any wall or floor
2. **NotTouchingService**: Fire stopping element is not connected to any service element
3. **Clashing**: Fire stopping element intersects with another fire stopping element
4. **Adjacent**: Fire stopping element is within 300mm of another fire stopping element

### User Interface
- **Collapsible Sidebar**: Filter controls with hamburger menu toggle
- **Material Design**: Modern WPF interface using MaterialDesignThemes
- **Real-time Filtering**: Level, category, and linked model filters
- **Progress Tracking**: Real-time progress updates during analysis
- **Summary Dashboard**: Quick overview of analysis results

## 🏗️ Architecture

### Technology Stack
- **.NET 6+**: Modern .NET framework
- **WPF + MaterialDesign**: Modern user interface
- **MVVM Pattern**: Clean separation of concerns using CommunityToolkit.Mvvm
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **Excel Export**: ClosedXML for robust Excel file generation

### Project Structure
```
MEP.Pacifire/
├── Models/                     # Data models and entities
│   ├── FireStoppingElement.cs  # Fire stopping element model
│   ├── ServiceElement.cs       # Service element model
│   ├── StructuralElement.cs    # Structural element model
│   ├── DesignCheckResult.cs    # Design check results
│   └── FilterSettings.cs       # Filter configuration
├── Services/                   # Business logic services
│   ├── IExtractionService.cs   # Element extraction interface
│   ├── ExtractionService.cs    # Element extraction implementation
│   ├── IDesignCheckService.cs  # Design check interface
│   ├── DesignCheckService.cs   # Design check implementation
│   ├── IExcelExportService.cs  # Excel export interface
│   ├── ExcelExportService.cs   # Excel export implementation
│   └── ServiceContainer.cs     # Dependency injection container
├── ViewModels/                 # MVVM ViewModels
│   └── MainViewModel.cs        # Main application ViewModel
├── Views/                      # WPF Views
│   ├── MainView.xaml           # Main application window
│   └── MainView.xaml.cs        # Main window code-behind
├── Helpers/                    # Utility classes
│   ├── IGeometryHelper.cs      # Geometry operations interface
│   ├── GeometryHelper.cs       # Geometry operations implementation
│   ├── ISpatialHelper.cs       # Spatial indexing interface
│   ├── SpatialHelper.cs        # Spatial indexing implementation
│   ├── IParameterHelper.cs     # Parameter extraction interface
│   ├── ParameterHelper.cs      # Parameter extraction implementation
│   ├── RevitHelper.cs          # Revit API utilities
│   └── FilterHelper.cs         # Filtering utilities
└── RevitCommands/              # Revit command entry points
    └── PacifireCommand.cs      # Main command implementation
```

## 🔧 Installation

### Prerequisites
- Autodesk Revit 2020-2026
- .NET 6.0 or later
- Windows 10/11

### Installation Steps
1. Download the latest release from the releases page
2. Copy the add-in files to your Revit add-ins folder
3. Restart Revit
4. The add-in will appear in the Revit ribbon

## 📖 Usage

### Getting Started
1. Open your Passive Fire host model in Revit
2. Ensure all required linked models are loaded (MEP, Architectural)
3. Launch the "Passive Fire Stopping Solution Exporter" from the ribbon
4. Configure your filters in the sidebar
5. Click "Extract & Analyze" to begin the analysis
6. Review results and export to Excel

### Filter Configuration
- **Linked Models**: Select which linked models to include in the analysis
- **Levels**: Filter by specific building levels
- **Categories**: Choose element categories (Duct Fittings, Pipe Fittings, etc.)
- **Search**: Text-based filtering across element properties

### Design Check Results
Results are color-coded in the main data grid:
- 🟢 **PASS**: Check passed successfully
- 🔴 **FAIL**: Check failed, requires attention

### Excel Export
The Excel export includes multiple sheets:
- **Main Data**: Complete element data with grouped columns
- **Summary**: Analysis statistics and overview
- **Design Checks**: Detailed check results
- **Metadata**: Export information and applied filters

## 🔍 Critical Features

### Geometric Intersection Logic
The tool's accuracy depends on precise coordinate transformation:
- All elements from linked models are transformed to host coordinate system
- Uses `RevitLinkInstance.GetTransform()` for accurate positioning
- Implements spatial indexing for performance optimization
- Handles coordinate system mismatches and origin differences

### Performance Optimization
- **Spatial Indexing**: Grid-based partitioning for efficient proximity searches
- **Bounding Box Filtering**: Quick elimination of non-intersecting elements
- **Progressive Loading**: Real-time progress updates during long operations
- **Memory Management**: Efficient handling of large datasets

## 🧪 Testing

### Unit Testing
The architecture is designed for comprehensive unit testing:
- Service interfaces enable easy mocking
- Dependency injection supports test isolation
- Helper classes have minimal dependencies

### Integration Testing
- Test with various Revit model configurations
- Validate coordinate transformation accuracy
- Verify Excel export formatting and data integrity

## 🔧 Configuration

### Custom Parameters
The tool supports custom parameter mappings for extensibility:
```csharp
var parameterHelper = ServiceContainer.GetService<IParameterHelper>();
parameterHelper.SetCustomParameterMappings(new Dictionary<string, string>
{
    ["Custom Field 1"] = "Project_Custom_Parameter_1",
    ["Custom Field 2"] = "Project_Custom_Parameter_2"
});
```

### Export Settings
Customize Excel export behavior:
```csharp
var exportSettings = new ExportSettings
{
    IncludeFireStoppingDetails = true,
    IncludeServiceDetails = true,
    IncludeStructureDetails = true,
    IncludeDesignChecks = true,
    ApplyFormatting = true,
    ProjectName = "Custom Project Name"
};
```

## 🤝 Contributing

### Development Setup
1. Clone the repository
2. Open in Visual Studio 2022
3. Restore NuGet packages
4. Build the solution
5. Deploy to Revit add-ins folder for testing

### Code Standards
- Follow C# coding conventions
- Use XML documentation comments
- Implement proper error handling
- Write unit tests for new features

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation wiki

## 🔄 Version History

### v1.0.0 (Current)
- Initial release
- Core extraction and analysis functionality
- Excel export with grouped sections
- Material Design UI with collapsible sidebar
- Comprehensive design checks
- Performance-optimized spatial analysis

## 🚀 Future Enhancements

- **BIM 360/ACC Integration**: Direct issue creation in cloud platforms
- **3D Visualization**: Interactive 3D view of analysis results
- **Custom Rules Engine**: User-defined validation rules
- **Automated Reporting**: Scheduled analysis and reporting
- **Mobile Dashboard**: Mobile app for field verification

## 🏆 Acknowledgments

- Built for Passive Fire Engineers
- Developed by Tristan Balme and Firza Utama
- Uses MaterialDesignThemes for modern UI
- Powered by ClosedXML for Excel generation
