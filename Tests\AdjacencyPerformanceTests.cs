using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using MEP.Pacifire.Services;
using MEP.Pacifire.Helpers;

namespace MEP.Pacifire.Tests
{
    /// <summary>
    /// Test class to demonstrate and validate the performance optimizations for CheckAdjacent method
    /// </summary>
    public static class AdjacencyPerformanceTests
    {
        /// <summary>
        /// Creates test fire stopping elements for performance testing
        /// </summary>
        public static List<FireStoppingElement> CreateTestElements(int count, double spacing = 10.0)
        {
            var elements = new List<FireStoppingElement>();
            var random = new Random(42); // Fixed seed for reproducible results

            for (int i = 0; i < count; i++)
            {
                var element = new FireStoppingElement
                {
                    ElementId = new ElementId(1000 + i),
                    LocationPoint = new XYZ(
                        i * spacing + random.NextDouble() * spacing * 0.1, // Add small random offset
                        (i / 10) * spacing + random.NextDouble() * spacing * 0.1,
                        random.NextDouble() * spacing * 0.1),
                    FamilyName = $"TestFireStop_{i}",
                    BecaInstMark = $"FS-{i:D4}",
                    DesignCheckResult = new DesignCheckResult()
                };

                // Create simple bounding box
                var size = 1.0; // 1 foot
                element.BoundingBox = new BoundingBoxXYZ
                {
                    Min = new XYZ(element.LocationPoint.X - size/2, element.LocationPoint.Y - size/2, element.LocationPoint.Z - size/2),
                    Max = new XYZ(element.LocationPoint.X + size/2, element.LocationPoint.Y + size/2, element.LocationPoint.Z + size/2)
                };

                elements.Add(element);
            }

            return elements;
        }

        /// <summary>
        /// Runs a comprehensive performance test comparing legacy and optimized methods
        /// </summary>
        public static string RunPerformanceTest(IDesignCheckService designCheckService, int elementCount = 100)
        {
            var testElements = CreateTestElements(elementCount);
            
            try
            {
                var report = designCheckService.AnalyzeAdjacencyPerformance(testElements, 300.0);
                return $"Performance Test Results for {elementCount} elements:\n\n{report}";
            }
            catch (Exception ex)
            {
                return $"Performance test failed: {ex.Message}\n{ex.StackTrace}";
            }
        }

        /// <summary>
        /// Validates that both methods produce the same results
        /// </summary>
        public static string ValidateConsistency(IDesignCheckService designCheckService, ISpatialHelper spatialHelper, int elementCount = 50)
        {
            var testElements = CreateTestElements(elementCount);
            var spatialIndex = spatialHelper.CreateSpatialIndex(testElements, Enumerable.Empty<StructuralElement>(), Enumerable.Empty<ServiceElement>());
            
            var results = new System.Text.StringBuilder();
            results.AppendLine($"Consistency Validation for {elementCount} elements:");
            results.AppendLine();

            int consistentResults = 0;
            int inconsistentResults = 0;
            var adjacencyThreshold = 300.0;

            foreach (var element in testElements.Take(Math.Min(10, elementCount))) // Test first 10 elements
            {
                try
                {
                    // Test legacy method
                    var legacyResult = designCheckService.CheckAdjacent(element, testElements, adjacencyThreshold);
                    
                    // Test optimized method
                    var optimizedResult = designCheckService.CheckAdjacentOptimized(element, spatialIndex, adjacencyThreshold);

                    // Compare results
                    var legacyAdjacent = legacyResult.AdjacentElements.ToHashSet();
                    var optimizedAdjacent = optimizedResult.AdjacentElements.ToHashSet();

                    bool isConsistent = legacyResult.IsAdjacent == optimizedResult.IsAdjacent &&
                                       legacyAdjacent.SetEquals(optimizedAdjacent);

                    if (isConsistent)
                    {
                        consistentResults++;
                        results.AppendLine($"✓ Element {element.BecaInstMark}: Consistent ({legacyAdjacent.Count} adjacent)");
                    }
                    else
                    {
                        inconsistentResults++;
                        results.AppendLine($"✗ Element {element.BecaInstMark}: INCONSISTENT");
                        results.AppendLine($"  Legacy: {legacyResult.IsAdjacent} ({legacyAdjacent.Count} adjacent)");
                        results.AppendLine($"  Optimized: {optimizedResult.IsAdjacent} ({optimizedAdjacent.Count} adjacent)");
                    }
                }
                catch (Exception ex)
                {
                    inconsistentResults++;
                    results.AppendLine($"✗ Element {element.BecaInstMark}: ERROR - {ex.Message}");
                }
            }

            results.AppendLine();
            results.AppendLine($"Summary: {consistentResults} consistent, {inconsistentResults} inconsistent");
            
            if (inconsistentResults == 0)
            {
                results.AppendLine("✓ All tests passed - methods produce consistent results!");
            }
            else
            {
                results.AppendLine("⚠ Some inconsistencies found - review implementation");
            }

            return results.ToString();
        }

        /// <summary>
        /// Tests cache effectiveness
        /// </summary>
        public static string TestCacheEffectiveness(IDesignCheckService designCheckService, ISpatialHelper spatialHelper, int elementCount = 100)
        {
            var testElements = CreateTestElements(elementCount);
            var spatialIndex = spatialHelper.CreateSpatialIndex(testElements, Enumerable.Empty<StructuralElement>(), Enumerable.Empty<ServiceElement>());
            
            var results = new System.Text.StringBuilder();
            results.AppendLine($"Cache Effectiveness Test for {elementCount} elements:");
            results.AppendLine();

            // Clear cache before test
            SpatialHelper.ClearDistanceCache();
            var initialStats = SpatialHelper.GetCacheStatistics();
            results.AppendLine($"Initial cache: {initialStats.Count} entries, {initialStats.MemoryEstimate} bytes");

            // Run first pass
            foreach (var element in testElements)
            {
                designCheckService.CheckAdjacentOptimized(element, spatialIndex, 300.0);
            }

            var firstPassStats = SpatialHelper.GetCacheStatistics();
            results.AppendLine($"After first pass: {firstPassStats.Count} entries, {firstPassStats.MemoryEstimate} bytes");

            // Run second pass (should hit cache more)
            var startTime = DateTime.Now;
            foreach (var element in testElements)
            {
                designCheckService.CheckAdjacentOptimized(element, spatialIndex, 300.0);
            }
            var secondPassTime = DateTime.Now - startTime;

            var finalStats = SpatialHelper.GetCacheStatistics();
            results.AppendLine($"After second pass: {finalStats.Count} entries, {finalStats.MemoryEstimate} bytes");
            results.AppendLine($"Second pass time: {secondPassTime.TotalMilliseconds:F2} ms");

            // Calculate cache growth
            var cacheGrowth = finalStats.Count - firstPassStats.Count;
            results.AppendLine($"Cache growth in second pass: {cacheGrowth} entries");

            if (cacheGrowth < firstPassStats.Count * 0.1) // Less than 10% growth
            {
                results.AppendLine("✓ Cache is working effectively - minimal growth in second pass");
            }
            else
            {
                results.AppendLine("⚠ Cache may not be working optimally - significant growth in second pass");
            }

            return results.ToString();
        }

        /// <summary>
        /// Runs all performance tests and generates a comprehensive report
        /// </summary>
        public static string RunAllTests(IDesignCheckService designCheckService, ISpatialHelper spatialHelper)
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== COMPREHENSIVE ADJACENCY PERFORMANCE TEST SUITE ===");
            report.AppendLine($"Test run at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // Test 1: Small dataset performance
            report.AppendLine("TEST 1: Small Dataset Performance (100 elements)");
            report.AppendLine(new string('=', 50));
            report.AppendLine(RunPerformanceTest(designCheckService, 100));
            report.AppendLine();

            // Test 2: Consistency validation
            report.AppendLine("TEST 2: Consistency Validation");
            report.AppendLine(new string('=', 50));
            report.AppendLine(ValidateConsistency(designCheckService, spatialHelper, 50));
            report.AppendLine();

            // Test 3: Cache effectiveness
            report.AppendLine("TEST 3: Cache Effectiveness");
            report.AppendLine(new string('=', 50));
            report.AppendLine(TestCacheEffectiveness(designCheckService, spatialHelper, 100));
            report.AppendLine();

            // Test 4: Larger dataset performance
            report.AppendLine("TEST 4: Larger Dataset Performance (500 elements)");
            report.AppendLine(new string('=', 50));
            report.AppendLine(RunPerformanceTest(designCheckService, 500));
            report.AppendLine();

            report.AppendLine("=== TEST SUITE COMPLETE ===");
            return report.ToString();
        }
    }
}
