using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Helper class for spatial indexing and optimization operations.
    /// Critical for performance when dealing with large numbers of elements.
    /// </summary>
    public class SpatialHelper : ISpatialHelper
    {
        private readonly IGeometryHelper _geometryHelper;

        public SpatialHelper(IGeometryHelper geometryHelper)
        {
            _geometryHelper = geometryHelper ?? throw new ArgumentNullException(nameof(geometryHelper));
        }

        /// <summary>
        /// Creates a spatial index for efficient proximity searches
        /// </summary>
        public SpatialIndex CreateSpatialIndex(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            IEnumerable<StructuralElement> structuralElements,
            IEnumerable<ServiceElement> serviceElements)
        {
            var fireStoppingList = fireStoppingElements.ToList();
            var structuralList = structuralElements.ToList();
            var serviceList = serviceElements.ToList();

            // Calculate overall bounds
            var overallBounds = CalculateOverallBounds(fireStoppingList, structuralList, serviceList);
            
            var spatialIndex = new SpatialIndex
            {
                OverallBounds = overallBounds,
                GridSize = CalculateOptimalGridSize(fireStoppingList.Count + structuralList.Count + serviceList.Count)
            };

            // Index fire stopping elements
            foreach (var element in fireStoppingList)
            {
                var gridKey = GetGridKey(element.LocationPoint, spatialIndex);
                if (!spatialIndex.FireStoppingGrid.ContainsKey(gridKey))
                {
                    spatialIndex.FireStoppingGrid[gridKey] = new List<FireStoppingElement>();
                }
                spatialIndex.FireStoppingGrid[gridKey].Add(element);
            }

            // Index structural elements
            foreach (var element in structuralList)
            {
                var gridKey = GetGridKey(element.LocationPoint, spatialIndex);
                if (!spatialIndex.StructuralGrid.ContainsKey(gridKey))
                {
                    spatialIndex.StructuralGrid[gridKey] = new List<StructuralElement>();
                }
                spatialIndex.StructuralGrid[gridKey].Add(element);
            }

            // Index service elements
            foreach (var element in serviceList)
            {
                var gridKey = GetGridKey(element.LocationPoint, spatialIndex);
                if (!spatialIndex.ServiceGrid.ContainsKey(gridKey))
                {
                    spatialIndex.ServiceGrid[gridKey] = new List<ServiceElement>();
                }
                spatialIndex.ServiceGrid[gridKey].Add(element);
            }

            return spatialIndex;
        }

        /// <summary>
        /// Finds elements within a specified distance of a target element using spatial index
        /// </summary>
        public SpatialSearchResult FindNearbyElements(
            SpatialIndex spatialIndex,
            FireStoppingElement targetElement,
            double searchDistance)
        {
            var result = new SpatialSearchResult
            {
                SearchCenter = targetElement.LocationPoint,
                SearchDistance = searchDistance
            };

            var searchRadius = (int)Math.Ceiling(searchDistance / spatialIndex.GridSize) + 1;
            var targetGridKey = GetGridKey(targetElement.LocationPoint, spatialIndex);
            var searchKeys = GetAdjacentGridKeys(targetGridKey, searchRadius, spatialIndex);

            // Search fire stopping elements
            foreach (var key in searchKeys)
            {
                if (spatialIndex.FireStoppingGrid.TryGetValue(key, out var fireStoppingElements))
                {
                    foreach (var element in fireStoppingElements)
                    {
                        if (element.ElementId != targetElement.ElementId)
                        {
                            var distance = _geometryHelper.CalculateDistance(targetElement.LocationPoint, element.LocationPoint);
                            if (distance <= searchDistance)
                            {
                                result.NearbyFireStoppingElements.Add(element);
                            }
                        }
                    }
                }
            }

            // Search structural elements
            foreach (var key in searchKeys)
            {
                if (spatialIndex.StructuralGrid.TryGetValue(key, out var structuralElements))
                {
                    foreach (var element in structuralElements)
                    {
                        var distance = _geometryHelper.CalculateDistance(targetElement.LocationPoint, element.LocationPoint);
                        if (distance <= searchDistance)
                        {
                            result.NearbyStructuralElements.Add(element);
                        }
                    }
                }
            }

            // Search service elements
            foreach (var key in searchKeys)
            {
                if (spatialIndex.ServiceGrid.TryGetValue(key, out var serviceElements))
                {
                    foreach (var element in serviceElements)
                    {
                        var distance = _geometryHelper.CalculateDistance(targetElement.LocationPoint, element.LocationPoint);
                        if (distance <= searchDistance)
                        {
                            result.NearbyServiceElements.Add(element);
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Finds structural elements that could intersect with a fire stopping element
        /// </summary>
        public IEnumerable<StructuralElement> FindPotentialStructuralIntersections(
            SpatialIndex spatialIndex,
            FireStoppingElement fireStoppingElement,
            double tolerance = 0.1)
        {
            var searchResult = FindNearbyElements(spatialIndex, fireStoppingElement, tolerance * 2);
            var candidates = new List<StructuralElement>();

            foreach (var structural in searchResult.NearbyStructuralElements)
            {
                // Quick bounding box check first
                if (fireStoppingElement.BoundingBox != null && structural.BoundingBox != null)
                {
                    var expandedBox = _geometryHelper.ExpandBoundingBox(fireStoppingElement.BoundingBox, tolerance);
                    if (_geometryHelper.DoBoundingBoxesIntersect(expandedBox, structural.BoundingBox))
                    {
                        candidates.Add(structural);
                    }
                }
                else
                {
                    // Fallback to distance check
                    var distance = _geometryHelper.CalculateDistance(fireStoppingElement.LocationPoint, structural.LocationPoint);
                    if (distance <= tolerance * 2)
                    {
                        candidates.Add(structural);
                    }
                }
            }

            return candidates;
        }

        /// <summary>
        /// Finds service elements that could be connected to a fire stopping element
        /// </summary>
        public IEnumerable<ServiceElement> FindPotentialServiceConnections(
            SpatialIndex spatialIndex,
            FireStoppingElement fireStoppingElement,
            double connectionTolerance = 0.2)
        {
            var searchResult = FindNearbyElements(spatialIndex, fireStoppingElement, connectionTolerance * 2);
            var candidates = new List<ServiceElement>();

            foreach (var service in searchResult.NearbyServiceElements)
            {
                // Check if within connection tolerance
                var distance = _geometryHelper.CalculateDistance(fireStoppingElement.LocationPoint, service.LocationPoint);
                if (distance <= connectionTolerance)
                {
                    candidates.Add(service);
                }
            }

            return candidates.OrderBy(s => _geometryHelper.CalculateDistance(fireStoppingElement.LocationPoint, s.LocationPoint));
        }

        /// <summary>
        /// Finds fire stopping elements that could be clashing or adjacent
        /// </summary>
        public IEnumerable<FireStoppingElement> FindPotentialFireStoppingProximity(
            SpatialIndex spatialIndex,
            FireStoppingElement fireStoppingElement,
            double adjacencyDistance)
        {
            var searchResult = FindNearbyElements(spatialIndex, fireStoppingElement, adjacencyDistance);
            return searchResult.NearbyFireStoppingElements
                .OrderBy(e => _geometryHelper.CalculateDistance(fireStoppingElement.LocationPoint, e.LocationPoint));
        }

        /// <summary>
        /// Optimizes the search order for elements based on spatial proximity
        /// </summary>
        public IEnumerable<T> OptimizeSearchOrder<T>(FireStoppingElement targetElement, IEnumerable<T> candidateElements)
            where T : class
        {
            var elementsList = candidateElements.ToList();
            
            // Sort by distance to target element
            return elementsList.OrderBy(element =>
            {
                XYZ elementLocation = XYZ.Zero;

                if (element is FireStoppingElement fireElement)
                    elementLocation = fireElement.LocationPoint;
                else if (element is ServiceElement serviceElement)
                    elementLocation = serviceElement.LocationPoint;
                else if (element is StructuralElement structuralElement)
                    elementLocation = structuralElement.LocationPoint;

                return _geometryHelper.CalculateDistance(targetElement.LocationPoint, elementLocation);
            });
        }

        /// <summary>
        /// Creates a spatial grid for partitioning elements
        /// </summary>
        public SpatialGrid CreateSpatialGrid(IEnumerable<object> allElements, double gridSize = 10.0)
        {
            var elementsList = allElements.ToList();
            var bounds = CalculateElementsBounds(elementsList);

            var grid = new SpatialGrid
            {
                CellSize = gridSize,
                Bounds = bounds
            };

            if (bounds != null)
            {
                grid.GridWidth = (int)Math.Ceiling((bounds.Max.X - bounds.Min.X) / gridSize) + 1;
                grid.GridHeight = (int)Math.Ceiling((bounds.Max.Y - bounds.Min.Y) / gridSize) + 1;
                grid.GridDepth = (int)Math.Ceiling((bounds.Max.Z - bounds.Min.Z) / gridSize) + 1;

                // Populate grid
                foreach (var element in elementsList)
                {
                    var location = GetElementLocationFromObject(element);
                    if (location != null)
                    {
                        var key = grid.GetGridKey(location);
                        if (!grid.Grid.ContainsKey(key))
                        {
                            grid.Grid[key] = new List<object>();
                        }
                        grid.Grid[key].Add(element);
                    }
                }
            }

            return grid;
        }

        /// <summary>
        /// Gets elements in the same grid cell as the target element
        /// </summary>
        public IEnumerable<object> GetElementsInSameCell(SpatialGrid spatialGrid, object targetElement)
        {
            var location = GetElementLocationFromObject(targetElement);
            if (location == null) return Enumerable.Empty<object>();

            var key = spatialGrid.GetGridKey(location);
            return spatialGrid.Grid.TryGetValue(key, out var elements) ? elements : Enumerable.Empty<object>();
        }

        /// <summary>
        /// Gets elements in adjacent grid cells
        /// </summary>
        public IEnumerable<object> GetElementsInAdjacentCells(SpatialGrid spatialGrid, object targetElement, int cellRadius = 1)
        {
            var location = GetElementLocationFromObject(targetElement);
            if (location == null) return Enumerable.Empty<object>();

            var centerKey = spatialGrid.GetGridKey(location);
            var adjacentKeys = spatialGrid.GetAdjacentKeys(centerKey, cellRadius);
            var result = new List<object>();

            foreach (var key in adjacentKeys)
            {
                if (spatialGrid.Grid.TryGetValue(key, out var elements))
                {
                    result.AddRange(elements);
                }
            }

            return result;
        }

        #region Private Helper Methods

        private BoundingBoxXYZ CalculateOverallBounds(
            List<FireStoppingElement> fireStoppingElements,
            List<StructuralElement> structuralElements,
            List<ServiceElement> serviceElements)
        {
            var allPoints = new List<XYZ>();

            allPoints.AddRange(fireStoppingElements.Select(e => e.LocationPoint));
            allPoints.AddRange(structuralElements.Select(e => e.LocationPoint));
            allPoints.AddRange(serviceElements.Select(e => e.LocationPoint));

            if (!allPoints.Any()) return null;

            var minX = allPoints.Min(p => p.X);
            var minY = allPoints.Min(p => p.Y);
            var minZ = allPoints.Min(p => p.Z);
            var maxX = allPoints.Max(p => p.X);
            var maxY = allPoints.Max(p => p.Y);
            var maxZ = allPoints.Max(p => p.Z);

            return new BoundingBoxXYZ
            {
                Min = new XYZ(minX, minY, minZ),
                Max = new XYZ(maxX, maxY, maxZ)
            };
        }

        private double CalculateOptimalGridSize(int totalElements)
        {
            // Heuristic: aim for roughly 10-50 elements per grid cell
            var targetElementsPerCell = Math.Max(10, Math.Min(50, totalElements / 100));
            return Math.Max(5.0, Math.Min(50.0, Math.Sqrt(totalElements / targetElementsPerCell)));
        }

        private string GetGridKey(XYZ point, SpatialIndex spatialIndex)
        {
            if (spatialIndex.OverallBounds == null) return "0,0,0";

            var x = (int)Math.Floor((point.X - spatialIndex.OverallBounds.Min.X) / spatialIndex.GridSize);
            var y = (int)Math.Floor((point.Y - spatialIndex.OverallBounds.Min.Y) / spatialIndex.GridSize);
            var z = (int)Math.Floor((point.Z - spatialIndex.OverallBounds.Min.Z) / spatialIndex.GridSize);

            return $"{x},{y},{z}";
        }

        private IEnumerable<string> GetAdjacentGridKeys(string centerKey, int radius, SpatialIndex spatialIndex)
        {
            var keys = new List<string>();
            var parts = centerKey.Split(',');
            
            if (parts.Length != 3) return keys;

            if (!int.TryParse(parts[0], out var centerX) ||
                !int.TryParse(parts[1], out var centerY) ||
                !int.TryParse(parts[2], out var centerZ))
            {
                return keys;
            }

            for (int x = centerX - radius; x <= centerX + radius; x++)
            {
                for (int y = centerY - radius; y <= centerY + radius; y++)
                {
                    for (int z = centerZ - radius; z <= centerZ + radius; z++)
                    {
                        keys.Add($"{x},{y},{z}");
                    }
                }
            }

            return keys;
        }

        private BoundingBoxXYZ? CalculateElementsBounds(List<object> elements)
        {
            var points = new List<XYZ>();

            foreach (var element in elements)
            {
                var location = GetElementLocationFromObject(element);
                if (location != null)
                {
                    points.Add(location);
                }
            }

            if (!points.Any()) return null;

            return new BoundingBoxXYZ
            {
                Min = new XYZ(points.Min(p => p.X), points.Min(p => p.Y), points.Min(p => p.Z)),
                Max = new XYZ(points.Max(p => p.X), points.Max(p => p.Y), points.Max(p => p.Z))
            };
        }

        private XYZ? GetElementLocationFromObject(object element)
        {
            return element switch
            {
                FireStoppingElement fireElement => fireElement.LocationPoint,
                ServiceElement serviceElement => serviceElement.LocationPoint,
                StructuralElement structuralElement => structuralElement.LocationPoint,
                _ => null
            };
        }

        #endregion
    }
}
