# Compatibility Fixes for .NET Framework

## Issue Resolution

The original implementation used `File.WriteAllTextAsync` and `File.ReadAllLinesAsync` methods which are not available in .NET Framework (they were introduced in .NET Core/.NET 5+). Since Revit add-ins typically target .NET Framework 4.8, these methods needed to be replaced.

## Solution Implemented

### 1. Created FileHelper Class
- **Location**: `Helpers/FileHelper.cs`
- **Purpose**: Provides async file operations compatible with .NET Framework
- **Implementation**: Wraps synchronous `File.*` methods in `Task.Run()` for async compatibility

### 2. Updated Project Targets
- **Main Project**: Uses Nice3point.Revit.Build.Tasks which automatically targets appropriate .NET Framework version based on Revit version
- **Test Project**: Updated to target .NET Framework 4.8 (`net48`)

### 3. Updated File Operations
- **Logger.cs**: Updated to use `FileHelper.AppendAllTextAsync` and `FileHelper.ReadAllLinesAsync`
- **DiagnosticHelper.cs**: Updated to use `FileHelper.WriteAllTextAsync`

## FileHelper Methods Available

### Basic Operations
- `WriteAllTextAsync(path, contents)` - Write text to file
- `AppendAllTextAsync(path, contents)` - Append text to file
- `ReadAllTextAsync(path)` - Read all text from file
- `ReadAllLinesAsync(path)` - Read all lines from file

### Advanced Operations
- `WriteAllLinesAsync(path, lines)` - Write lines to file
- `ReadAllBytesAsync(path)` - Read bytes from file
- `WriteAllBytesAsync(path, bytes)` - Write bytes to file
- `SafeWriteAllTextAsync(path, contents)` - Safe write with backup/rollback

### File Management
- `ExistsAsync(path)` - Check if file exists
- `DeleteAsync(path)` - Delete file
- `CopyAsync(source, dest, overwrite)` - Copy file
- `MoveAsync(source, dest)` - Move file
- `GetFileInfoAsync(path)` - Get file information

## Benefits of This Approach

### 1. **Compatibility**
- Works with all .NET Framework versions (4.6.1+)
- Compatible with all Revit versions (2020-2026)
- No external dependencies required

### 2. **Performance**
- Uses `Task.Run()` to offload I/O operations to thread pool
- Maintains async/await pattern for non-blocking UI
- Efficient for file operations in Revit context

### 3. **Safety**
- `SafeWriteAllTextAsync` provides backup/rollback functionality
- Proper exception handling throughout
- Maintains data integrity during file operations

### 4. **Consistency**
- Same API as .NET Core/5+ async file methods
- Easy to migrate to newer .NET versions in future
- Consistent error handling patterns

## Usage Examples

```csharp
// Writing text to file
await FileHelper.WriteAllTextAsync("output.txt", "Hello World");

// Appending to log file
await FileHelper.AppendAllTextAsync("log.txt", "New log entry\n");

// Reading configuration file
var config = await FileHelper.ReadAllTextAsync("config.json");

// Safe write with backup
await FileHelper.SafeWriteAllTextAsync("important.dat", criticalData);
```

## Migration Notes

If upgrading to .NET 5+ in the future, the FileHelper calls can be easily replaced with native `File.*Async` methods:

```csharp
// Current (.NET Framework compatible)
await FileHelper.WriteAllTextAsync(path, content);

// Future (.NET 5+ native)
await File.WriteAllTextAsync(path, content);
```

## Testing

The FileHelper class maintains the same behavior as the native async file methods:
- Same exception types thrown
- Same parameter validation
- Same file handling behavior
- Compatible with existing unit tests

## Conclusion

This compatibility fix ensures the Passive Fire Stopping Solution Exporter works correctly with all supported Revit versions while maintaining modern async/await patterns and providing a clear upgrade path for future .NET versions.
