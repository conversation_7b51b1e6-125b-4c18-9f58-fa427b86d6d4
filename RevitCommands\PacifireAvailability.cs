using Autodesk.Revit.DB;
using Autodesk.Revit.UI;

namespace MEP.Pacifire.RevitCommands
{
    /// <summary>
    /// Availability class for the Pacifire command.
    /// Determines when the command should be available in the Revit UI.
    /// </summary>
    public class PacifireAvailability : IExternalCommandAvailability
    {
        /// <summary>
        /// Determines if the command is available based on the current Revit context
        /// </summary>
        /// <param name="applicationData">Revit application data</param>
        /// <param name="selectedCategories">Currently selected categories</param>
        /// <returns>True if command should be available</returns>
        public bool IsCommandAvailable(UIApplication applicationData, CategorySet selectedCategories)
        {
            // Check if we have an active document
            if (applicationData?.ActiveUIDocument?.Document == null)
                return false;

            var document = applicationData.ActiveUIDocument.Document;

            // Don't allow in family documents
            if (document.IsFamilyDocument)
                return false;

            // Don't allow in read-only documents
            if (document.IsReadOnly)
                return false;

            // Command is available for project documents
            return true;
        }
    }
}
