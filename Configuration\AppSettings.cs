using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MEP.Pacifire.Configuration
{
    /// <summary>
    /// Application settings and configuration management.
    /// Handles user preferences, default values, and settings persistence.
    /// </summary>
    public class AppSettings
    {
        private static readonly string SettingsFileName = "PacifireSettings.json";
        private static readonly string SettingsDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
            "MEP.Pacifire");

        private static AppSettings? _instance;
        internal bool EnableDebugLogging;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of AppSettings
        /// </summary>
        public static AppSettings Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= LoadSettings();
                    }
                }
                return _instance;
            }
        }

        #region UI Settings

        /// <summary>
        /// Whether the sidebar is collapsed by default
        /// </summary>
        public bool IsSidebarCollapsed { get; set; } = false;

        /// <summary>
        /// Last window width
        /// </summary>
        public double WindowWidth { get; set; } = 1400;

        /// <summary>
        /// Last window height
        /// </summary>
        public double WindowHeight { get; set; } = 800;

        /// <summary>
        /// Last window state (Normal, Maximized, etc.)
        /// </summary>
        public string WindowState { get; set; } = "Normal";

        /// <summary>
        /// Theme preference (Light, Dark, Auto)
        /// </summary>
        public string Theme { get; set; } = "Light";

        #endregion

        #region Analysis Settings

        /// <summary>
        /// Default adjacency threshold in millimeters
        /// </summary>
        public double DefaultAdjacencyThreshold { get; set; } = 300.0;

        /// <summary>
        /// Default connection tolerance in millimeters
        /// </summary>
        public double DefaultConnectionTolerance { get; set; } = 50.0;

        /// <summary>
        /// Default intersection tolerance in millimeters
        /// </summary>
        public double DefaultIntersectionTolerance { get; set; } = 1.0;

        /// <summary>
        /// Enable spatial indexing optimization
        /// </summary>
        public bool EnableSpatialIndexing { get; set; } = true;

        /// <summary>
        /// Spatial grid size in feet
        /// </summary>
        public double SpatialGridSize { get; set; } = 10.0;

        /// <summary>
        /// Maximum elements to process before showing warning
        /// </summary>
        public int MaxElementsWarningThreshold { get; set; } = 10000;

        #endregion

        #region Export Settings

        /// <summary>
        /// Default export directory
        /// </summary>
        public string DefaultExportDirectory { get; set; } = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);

        /// <summary>
        /// Include fire stopping details in export by default
        /// </summary>
        public bool DefaultIncludeFireStoppingDetails { get; set; } = true;

        /// <summary>
        /// Include service details in export by default
        /// </summary>
        public bool DefaultIncludeServiceDetails { get; set; } = true;

        /// <summary>
        /// Include structure details in export by default
        /// </summary>
        public bool DefaultIncludeStructureDetails { get; set; } = true;

        /// <summary>
        /// Include design checks in export by default
        /// </summary>
        public bool DefaultIncludeDesignChecks { get; set; } = true;

        /// <summary>
        /// Apply formatting to Excel export by default
        /// </summary>
        public bool DefaultApplyFormatting { get; set; } = true;

        /// <summary>
        /// Auto-fit columns in Excel export by default
        /// </summary>
        public bool DefaultAutoFitColumns { get; set; } = true;

        /// <summary>
        /// Add filters to Excel export by default
        /// </summary>
        public bool DefaultAddFilters { get; set; } = true;

        #endregion

        #region Filter Settings

        /// <summary>
        /// Remember last used filters
        /// </summary>
        public bool RememberLastFilters { get; set; } = true;

        /// <summary>
        /// Last selected levels (JSON serialized)
        /// </summary>
        public List<string> LastSelectedLevels { get; set; } = new();

        /// <summary>
        /// Last selected categories (JSON serialized)
        /// </summary>
        public List<string> LastSelectedCategories { get; set; } = new();

        /// <summary>
        /// Last selected linked models (JSON serialized)
        /// </summary>
        public List<string> LastSelectedLinkedModels { get; set; } = new();

        /// <summary>
        /// Last search text
        /// </summary>
        public string LastSearchText { get; set; } = string.Empty;

        /// <summary>
        /// Last failures only setting
        /// </summary>
        public bool LastShowFailuresOnly { get; set; } = false;

        #endregion

        #region Performance Settings

        /// <summary>
        /// Enable multi-threading for analysis
        /// </summary>
        public bool EnableMultiThreading { get; set; } = true;

        /// <summary>
        /// Maximum degree of parallelism
        /// </summary>
        public int MaxDegreeOfParallelism { get; set; } = Environment.ProcessorCount;

        /// <summary>
        /// Enable progress reporting
        /// </summary>
        public bool EnableProgressReporting { get; set; } = true;

        /// <summary>
        /// Progress update interval in milliseconds
        /// </summary>
        public int ProgressUpdateInterval { get; set; } = 100;

        /// <summary>
        /// Enable detailed logging
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = false;

        #endregion

        #region Custom Parameter Mappings

        /// <summary>
        /// Custom parameter mappings for extensibility
        /// </summary>
        public Dictionary<string, string> CustomParameterMappings { get; set; } = new();

        /// <summary>
        /// Custom design check rules
        /// </summary>
        public Dictionary<string, object> CustomDesignCheckRules { get; set; } = new();

        #endregion

        #region Methods

        /// <summary>
        /// Saves the current settings to file
        /// </summary>
        public void Save()
        {
            try
            {
                // Ensure directory exists
                Directory.CreateDirectory(SettingsDirectory);

                var filePath = Path.Combine(SettingsDirectory, SettingsFileName);
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    //DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                };

                var json = JsonSerializer.Serialize(this, options);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads settings from file or creates default settings
        /// </summary>
        /// <returns>AppSettings instance</returns>
        private static AppSettings LoadSettings()
        {
            try
            {
                var filePath = Path.Combine(SettingsDirectory, SettingsFileName);
                
                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true
                    };

                    var settings = JsonSerializer.Deserialize<AppSettings>(json, options);
                    if (settings != null)
                    {
                        return settings;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
            }

            // Return default settings if loading fails
            return new AppSettings();
        }

        /// <summary>
        /// Resets all settings to default values
        /// </summary>
        public void ResetToDefaults()
        {
            var defaultSettings = new AppSettings();
            
            // Copy all properties from default settings
            var properties = typeof(AppSettings).GetProperties();
            foreach (var property in properties)
            {
                if (property.CanWrite)
                {
                    var defaultValue = property.GetValue(defaultSettings);
                    property.SetValue(this, defaultValue);
                }
            }
        }

        /// <summary>
        /// Validates settings and corrects invalid values
        /// </summary>
        public void ValidateAndCorrect()
        {
            // Validate numeric ranges
            DefaultAdjacencyThreshold = Math.Max(1.0, Math.Min(10000.0, DefaultAdjacencyThreshold));
            DefaultConnectionTolerance = Math.Max(0.1, Math.Min(1000.0, DefaultConnectionTolerance));
            DefaultIntersectionTolerance = Math.Max(0.01, Math.Min(100.0, DefaultIntersectionTolerance));
            SpatialGridSize = Math.Max(1.0, Math.Min(100.0, SpatialGridSize));
            MaxElementsWarningThreshold = Math.Max(100, Math.Min(100000, MaxElementsWarningThreshold));
            MaxDegreeOfParallelism = Math.Max(1, Math.Min(Environment.ProcessorCount * 2, MaxDegreeOfParallelism));
            ProgressUpdateInterval = Math.Max(10, Math.Min(5000, ProgressUpdateInterval));

            // Validate window dimensions
            WindowWidth = Math.Max(800, Math.Min(3840, WindowWidth));
            WindowHeight = Math.Max(600, Math.Min(2160, WindowHeight));

            // Validate directories
            if (!Directory.Exists(DefaultExportDirectory))
            {
                DefaultExportDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            }

            // Validate theme
            if (!new[] { "Light", "Dark", "Auto" }.Contains(Theme))
            {
                Theme = "Light";
            }

            // Validate window state
            if (!new[] { "Normal", "Maximized", "Minimized" }.Contains(WindowState))
            {
                WindowState = "Normal";
            }
        }

        /// <summary>
        /// Gets the full path to the settings file
        /// </summary>
        public static string GetSettingsFilePath()
        {
            return Path.Combine(SettingsDirectory, SettingsFileName);
        }

        /// <summary>
        /// Checks if settings file exists
        /// </summary>
        public static bool SettingsFileExists()
        {
            return File.Exists(GetSettingsFilePath());
        }

        /// <summary>
        /// Deletes the settings file
        /// </summary>
        public static void DeleteSettingsFile()
        {
            try
            {
                var filePath = GetSettingsFilePath();
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting settings file: {ex.Message}");
            }
        }

        #endregion
    }
}
