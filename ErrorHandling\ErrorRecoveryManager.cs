using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MEP.Pacifire.Diagnostics;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.ErrorHandling
{
    /// <summary>
    /// Manages error recovery and resilience strategies for the Fire Stopping Solution Exporter.
    /// Provides automatic retry, fallback mechanisms, and graceful degradation.
    /// </summary>
    public class ErrorRecoveryManager
    {
        private readonly Logger _logger;
        private readonly Dictionary<Type, IErrorRecoveryStrategy> _strategies;

        public ErrorRecoveryManager()
        {
            _logger = Logger.Instance;
            _strategies = new Dictionary<Type, IErrorRecoveryStrategy>
            {
                [typeof(OutOfMemoryException)] = new MemoryRecoveryStrategy(),
                [typeof(UnauthorizedAccessException)] = new PermissionRecoveryStrategy(),
                [typeof(TimeoutException)] = new TimeoutRecoveryStrategy(),
                [typeof(OperationCanceledException)] = new CancellationRecoveryStrategy(),
                [typeof(InvalidOperationException)] = new InvalidOperationRecoveryStrategy()
            };
        }

        /// <summary>
        /// Executes an operation with automatic error recovery
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">Operation to execute</param>
        /// <param name="operationName">Name for logging</param>
        /// <param name="maxRetries">Maximum retry attempts</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Operation result</returns>
        public async Task<RecoveryResult<T>> ExecuteWithRecoveryAsync<T>(
            Func<Task<T>> operation,
            string operationName,
            int maxRetries = 3,
            CancellationToken cancellationToken = default)
        {
            var attempt = 0;
            var exceptions = new List<Exception>();

            while (attempt <= maxRetries)
            {
                try
                {
                    _logger.Debug($"Executing {operationName} (attempt {attempt + 1}/{maxRetries + 1})");
                    
                    var result = await operation();
                    
                    if (attempt > 0)
                    {
                        _logger.Information($"{operationName} succeeded after {attempt} retry attempt(s)");
                    }
                    
                    return RecoveryResult<T>.Success(result, attempt);
                }
                catch (Exception ex) when (!(ex is OperationCanceledException) || !cancellationToken.IsCancellationRequested)
                {
                    attempt++;
                    exceptions.Add(ex);
                    
                    _logger.Warning($"{operationName} failed on attempt {attempt}: {ex.Message}");

                    if (attempt > maxRetries)
                    {
                        _logger.Error($"{operationName} failed after {maxRetries + 1} attempts", ex);
                        break;
                    }

                    // Try to recover from the error
                    var recoveryResult = await TryRecoverFromErrorAsync(ex, operationName, attempt, cancellationToken);
                    
                    if (!recoveryResult.CanRetry)
                    {
                        _logger.Error($"{operationName} cannot be retried due to unrecoverable error", ex);
                        break;
                    }

                    // Wait before retry with exponential backoff
                    var delay = CalculateRetryDelay(attempt);
                    _logger.Debug($"Waiting {delay.TotalMilliseconds}ms before retry");
                    
                    try
                    {
                        await Task.Delay(delay, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.Information($"{operationName} cancelled during retry delay");
                        return RecoveryResult<T>.Cancelled();
                    }
                }
                catch (OperationCanceledException)
                {
                    _logger.Information($"{operationName} was cancelled");
                    return RecoveryResult<T>.Cancelled();
                }
            }

            return RecoveryResult<T>.Failed(exceptions);
        }

        /// <summary>
        /// Executes an operation with automatic error recovery (void return)
        /// </summary>
        /// <param name="operation">Operation to execute</param>
        /// <param name="operationName">Name for logging</param>
        /// <param name="maxRetries">Maximum retry attempts</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Recovery result</returns>
        public async Task<RecoveryResult> ExecuteWithRecoveryAsync(
            Func<Task> operation,
            string operationName,
            int maxRetries = 3,
            CancellationToken cancellationToken = default)
        {
            var result = await ExecuteWithRecoveryAsync(
                async () =>
                {
                    await operation();
                    return true;
                },
                operationName,
                maxRetries,
                cancellationToken);

            return new RecoveryResult
            {
                IsSuccess = result.IsSuccess,
                IsCancelled = result.IsCancelled,
                AttemptCount = result.AttemptCount,
                Exceptions = result.Exceptions,
                RecoveryActions = result.RecoveryActions
            };
        }

        /// <summary>
        /// Executes an operation with fallback
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="primaryOperation">Primary operation</param>
        /// <param name="fallbackOperation">Fallback operation</param>
        /// <param name="operationName">Name for logging</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Operation result</returns>
        public async Task<RecoveryResult<T>> ExecuteWithFallbackAsync<T>(
            Func<Task<T>> primaryOperation,
            Func<Task<T>> fallbackOperation,
            string operationName,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.Debug($"Executing primary operation for {operationName}");
                var result = await primaryOperation();
                return RecoveryResult<T>.Success(result, 0);
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                _logger.Warning($"Primary operation failed for {operationName}, trying fallback: {ex.Message}");
                
                try
                {
                    var fallbackResult = await fallbackOperation();
                    _logger.Information($"Fallback operation succeeded for {operationName}");
                    
                    var result = RecoveryResult<T>.Success(fallbackResult, 1);
                    result.RecoveryActions.Add($"Used fallback operation due to: {ex.Message}");
                    return result;
                }
                catch (Exception fallbackEx)
                {
                    _logger.Error($"Both primary and fallback operations failed for {operationName}", fallbackEx);
                    return RecoveryResult<T>.Failed(new[] { ex, fallbackEx });
                }
            }
            catch (OperationCanceledException)
            {
                _logger.Information($"{operationName} was cancelled");
                return RecoveryResult<T>.Cancelled();
            }
        }

        /// <summary>
        /// Registers a custom error recovery strategy
        /// </summary>
        /// <param name="exceptionType">Exception type to handle</param>
        /// <param name="strategy">Recovery strategy</param>
        public void RegisterStrategy(Type exceptionType, IErrorRecoveryStrategy strategy)
        {
            _strategies[exceptionType] = strategy;
            _logger.Debug($"Registered recovery strategy for {exceptionType.Name}");
        }

        /// <summary>
        /// Creates a circuit breaker for an operation
        /// </summary>
        /// <param name="operationName">Operation name</param>
        /// <param name="failureThreshold">Number of failures before opening circuit</param>
        /// <param name="timeout">Timeout before attempting to close circuit</param>
        /// <returns>Circuit breaker instance</returns>
        public CircuitBreaker CreateCircuitBreaker(string operationName, int failureThreshold = 5, TimeSpan? timeout = null)
        {
            return new CircuitBreaker(operationName, failureThreshold, timeout ?? TimeSpan.FromMinutes(1), _logger);
        }

        #region Private Methods

        private async Task<ErrorRecoveryResult> TryRecoverFromErrorAsync(
            Exception exception, 
            string operationName, 
            int attemptNumber, 
            CancellationToken cancellationToken)
        {
            var exceptionType = exception.GetType();
            
            // Try exact type match first
            if (_strategies.TryGetValue(exceptionType, out var strategy))
            {
                return await strategy.TryRecoverAsync(exception, operationName, attemptNumber, cancellationToken);
            }

            // Try base type matches
            foreach (var kvp in _strategies)
            {
                if (kvp.Key.IsAssignableFrom(exceptionType))
                {
                    return await kvp.Value.TryRecoverAsync(exception, operationName, attemptNumber, cancellationToken);
                }
            }

            // Default recovery strategy
            _logger.Debug($"No specific recovery strategy found for {exceptionType.Name}, using default");
            return new ErrorRecoveryResult
            {
                CanRetry = attemptNumber < 3, // Allow up to 3 retries by default
                RecoveryAction = "Generic retry with exponential backoff"
            };
        }

        private static TimeSpan CalculateRetryDelay(int attemptNumber)
        {
            // Exponential backoff with jitter
            var baseDelay = TimeSpan.FromMilliseconds(Math.Pow(2, attemptNumber) * 100);
            var jitter = TimeSpan.FromMilliseconds(new Random().Next(0, 100));
            return baseDelay + jitter;
        }

        #endregion
    }

    #region Supporting Classes and Interfaces

    /// <summary>
    /// Interface for error recovery strategies
    /// </summary>
    public interface IErrorRecoveryStrategy
    {
        Task<ErrorRecoveryResult> TryRecoverAsync(Exception exception, string operationName, int attemptNumber, CancellationToken cancellationToken);
    }

    /// <summary>
    /// Result of error recovery attempt
    /// </summary>
    public class ErrorRecoveryResult
    {
        public bool CanRetry { get; set; }
        public string RecoveryAction { get; set; } = string.Empty;
        public TimeSpan? SuggestedDelay { get; set; }
    }

    /// <summary>
    /// Result of operation with recovery
    /// </summary>
    public class RecoveryResult<T>
    {
        public bool IsSuccess { get; set; }
        public bool IsCancelled { get; set; }
        public T? Result { get; set; }
        public int AttemptCount { get; set; }
        public List<Exception> Exceptions { get; set; } = new();
        public List<string> RecoveryActions { get; set; } = new();

        public static RecoveryResult<T> Success(T result, int attemptCount)
        {
            return new RecoveryResult<T>
            {
                IsSuccess = true,
                Result = result,
                AttemptCount = attemptCount
            };
        }

        public static RecoveryResult<T> Failed(IEnumerable<Exception> exceptions)
        {
            return new RecoveryResult<T>
            {
                IsSuccess = false,
                Exceptions = new List<Exception>(exceptions)
            };
        }

        public static RecoveryResult<T> Cancelled()
        {
            return new RecoveryResult<T>
            {
                IsCancelled = true
            };
        }
    }

    /// <summary>
    /// Result of operation with recovery (void return)
    /// </summary>
    public class RecoveryResult
    {
        public bool IsSuccess { get; set; }
        public bool IsCancelled { get; set; }
        public int AttemptCount { get; set; }
        public List<Exception> Exceptions { get; set; } = new();
        public List<string> RecoveryActions { get; set; } = new();
    }

    /// <summary>
    /// Circuit breaker pattern implementation
    /// </summary>
    public class CircuitBreaker
    {
        private readonly string _operationName;
        private readonly int _failureThreshold;
        private readonly TimeSpan _timeout;
        private readonly Logger _logger;
        private int _failureCount;
        private DateTime _lastFailureTime;
        private CircuitState _state = CircuitState.Closed;

        public CircuitBreaker(string operationName, int failureThreshold, TimeSpan timeout, Logger logger)
        {
            _operationName = operationName;
            _failureThreshold = failureThreshold;
            _timeout = timeout;
            _logger = logger;
        }

        public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation)
        {
            if (_state == CircuitState.Open)
            {
                if (DateTime.Now - _lastFailureTime > _timeout)
                {
                    _state = CircuitState.HalfOpen;
                    _logger.Information($"Circuit breaker for {_operationName} is now half-open");
                }
                else
                {
                    throw new InvalidOperationException($"Circuit breaker is open for {_operationName}");
                }
            }

            try
            {
                var result = await operation();
                
                if (_state == CircuitState.HalfOpen)
                {
                    _state = CircuitState.Closed;
                    _failureCount = 0;
                    _logger.Information($"Circuit breaker for {_operationName} is now closed");
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _failureCount++;
                _lastFailureTime = DateTime.Now;
                
                if (_failureCount >= _failureThreshold)
                {
                    _state = CircuitState.Open;
                    _logger.Warning($"Circuit breaker for {_operationName} is now open after {_failureCount} failures");
                }
                
                throw;
            }
        }
    }

    public enum CircuitState
    {
        Closed,
        Open,
        HalfOpen
    }

    #endregion

    #region Recovery Strategies

    public class MemoryRecoveryStrategy : IErrorRecoveryStrategy
    {
        public async Task<ErrorRecoveryResult> TryRecoverAsync(Exception exception, string operationName, int attemptNumber, CancellationToken cancellationToken)
        {
            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            await Task.Delay(1000, cancellationToken); // Give time for cleanup
            
            return new ErrorRecoveryResult
            {
                CanRetry = attemptNumber < 2, // Only retry once for memory issues
                RecoveryAction = "Forced garbage collection and memory cleanup",
                SuggestedDelay = TimeSpan.FromSeconds(2)
            };
        }
    }

    public class PermissionRecoveryStrategy : IErrorRecoveryStrategy
    {
        public Task<ErrorRecoveryResult> TryRecoverAsync(Exception exception, string operationName, int attemptNumber, CancellationToken cancellationToken)
        {
            return Task.FromResult(new ErrorRecoveryResult
            {
                CanRetry = false, // Permission issues usually can't be resolved automatically
                RecoveryAction = "Permission denied - manual intervention required"
            });
        }
    }

    public class TimeoutRecoveryStrategy : IErrorRecoveryStrategy
    {
        public Task<ErrorRecoveryResult> TryRecoverAsync(Exception exception, string operationName, int attemptNumber, CancellationToken cancellationToken)
        {
            return Task.FromResult(new ErrorRecoveryResult
            {
                CanRetry = attemptNumber < 3,
                RecoveryAction = "Timeout occurred - will retry with longer timeout",
                SuggestedDelay = TimeSpan.FromSeconds(Math.Pow(2, attemptNumber))
            });
        }
    }

    public class CancellationRecoveryStrategy : IErrorRecoveryStrategy
    {
        public Task<ErrorRecoveryResult> TryRecoverAsync(Exception exception, string operationName, int attemptNumber, CancellationToken cancellationToken)
        {
            return Task.FromResult(new ErrorRecoveryResult
            {
                CanRetry = false, // Cancellation is intentional
                RecoveryAction = "Operation was cancelled by user"
            });
        }
    }

    public class InvalidOperationRecoveryStrategy : IErrorRecoveryStrategy
    {
        public Task<ErrorRecoveryResult> TryRecoverAsync(Exception exception, string operationName, int attemptNumber, CancellationToken cancellationToken)
        {
            return Task.FromResult(new ErrorRecoveryResult
            {
                CanRetry = attemptNumber < 2, // Limited retries for invalid operations
                RecoveryAction = "Invalid operation state - will retry once",
                SuggestedDelay = TimeSpan.FromSeconds(1)
            });
        }
    }

    #endregion
}
