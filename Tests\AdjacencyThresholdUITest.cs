using System;
using System.ComponentModel;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Tests
{
    /// <summary>
    /// Test class to verify adjacency threshold TextBox functionality
    /// </summary>
    public static class AdjacencyThresholdUITest
    {
        /// <summary>
        /// Tests the adjacency threshold property binding and validation
        /// </summary>
        public static void TestAdjacencyThresholdBinding()
        {
            Console.WriteLine("=== ADJACENCY THRESHOLD UI BINDING TEST ===");
            Console.WriteLine();

            var filterSettings = new FilterSettings();
            var propertyChangedCount = 0;
            var filterSummaryUpdates = 0;

            // Subscribe to PropertyChanged events
            filterSettings.PropertyChanged += (sender, e) =>
            {
                propertyChangedCount++;
                Console.WriteLine($"PropertyChanged: {e.PropertyName}");
                
                if (e.PropertyName == nameof(FilterSettings.FilterSummary))
                {
                    filterSummaryUpdates++;
                    Console.WriteLine($"  FilterSummary updated: \"{filterSettings.FilterSummary}\"");
                }
            };

            Console.WriteLine($"Initial AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm");
            Console.WriteLine($"Initial FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            // Test 1: Set to default value (should not show in summary)
            Console.WriteLine("TEST 1: Setting to default value (300mm)...");
            filterSettings.AdjacencyThreshold = 300.0;
            Console.WriteLine($"AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm");
            Console.WriteLine($"FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            // Test 2: Set to non-default value (should show in summary)
            Console.WriteLine("TEST 2: Setting to non-default value (500mm)...");
            filterSettings.AdjacencyThreshold = 500.0;
            Console.WriteLine($"AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm");
            Console.WriteLine($"FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            // Test 3: Set to minimum value
            Console.WriteLine("TEST 3: Setting to minimum value (1mm)...");
            filterSettings.AdjacencyThreshold = 1.0;
            Console.WriteLine($"AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm");
            Console.WriteLine($"FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            // Test 4: Set to maximum value
            Console.WriteLine("TEST 4: Setting to maximum value (10000mm)...");
            filterSettings.AdjacencyThreshold = 10000.0;
            Console.WriteLine($"AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm");
            Console.WriteLine($"FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            // Test 5: Try to set below minimum (should clamp to 1)
            Console.WriteLine("TEST 5: Trying to set below minimum (0.5mm)...");
            filterSettings.AdjacencyThreshold = 0.5;
            Console.WriteLine($"AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm (should be clamped to 1)");
            Console.WriteLine($"FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            // Test 6: Try to set above maximum (should clamp to 10000)
            Console.WriteLine("TEST 6: Trying to set above maximum (15000mm)...");
            filterSettings.AdjacencyThreshold = 15000.0;
            Console.WriteLine($"AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm (should be clamped to 10000)");
            Console.WriteLine($"FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            // Test 7: Reset to default
            Console.WriteLine("TEST 7: Resetting filters...");
            filterSettings.Reset();
            Console.WriteLine($"AdjacencyThreshold: {filterSettings.AdjacencyThreshold}mm");
            Console.WriteLine($"FilterSummary: \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            Console.WriteLine($"Total PropertyChanged events: {propertyChangedCount}");
            Console.WriteLine($"FilterSummary updates: {filterSummaryUpdates}");
            Console.WriteLine();

            Console.WriteLine("EXPECTED BEHAVIOR:");
            Console.WriteLine("✓ AdjacencyThreshold should bind to TextBox Text property");
            Console.WriteLine("✓ Values should be clamped to 1-10000mm range");
            Console.WriteLine("✓ FilterSummary should show threshold when != 300mm");
            Console.WriteLine("✓ FilterSummary should hide threshold when == 300mm");
            Console.WriteLine("✓ PropertyChanged events should fire for each change");
        }

        /// <summary>
        /// Tests the FilterSummary display format for adjacency threshold
        /// </summary>
        public static void TestFilterSummaryFormat()
        {
            Console.WriteLine("=== FILTER SUMMARY FORMAT TEST ===");
            Console.WriteLine();

            var filterSettings = new FilterSettings();

            var testCases = new[]
            {
                new { Threshold = 300.0, Expected = "Should not show threshold (default)" },
                new { Threshold = 299.9, Expected = "Should show threshold (close to default)" },
                new { Threshold = 300.1, Expected = "Should show threshold (close to default)" },
                new { Threshold = 150.0, Expected = "Should show 'Threshold: 150mm'" },
                new { Threshold = 600.0, Expected = "Should show 'Threshold: 600mm'" },
                new { Threshold = 1.0, Expected = "Should show 'Threshold: 1mm'" },
                new { Threshold = 10000.0, Expected = "Should show 'Threshold: 10000mm'" }
            };

            foreach (var testCase in testCases)
            {
                filterSettings.Reset();
                filterSettings.AdjacencyThreshold = testCase.Threshold;
                
                var showsThreshold = filterSettings.FilterSummary.Contains("Threshold:");
                var expectedToShow = Math.Abs(testCase.Threshold - 300.0) > 0.1;
                
                Console.WriteLine($"Threshold: {testCase.Threshold}mm");
                Console.WriteLine($"  FilterSummary: \"{filterSettings.FilterSummary}\"");
                Console.WriteLine($"  Shows threshold: {showsThreshold}");
                Console.WriteLine($"  Expected to show: {expectedToShow}");
                Console.WriteLine($"  Result: {(showsThreshold == expectedToShow ? "✓ PASS" : "✗ FAIL")}");
                Console.WriteLine();
            }
        }

        /// <summary>
        /// Tests the adjacency threshold with other filters
        /// </summary>
        public static void TestThresholdWithOtherFilters()
        {
            Console.WriteLine("=== THRESHOLD WITH OTHER FILTERS TEST ===");
            Console.WriteLine();

            var filterSettings = new FilterSettings();

            // Add some filter selections
            filterSettings.SelectedLevels.Add(new LevelFilter("Level 1", true));
            filterSettings.SelectedCategories.Add(new CategoryFilter("Duct Fitting", true));
            filterSettings.ShowFailuresOnly = true;
            filterSettings.SearchText = "test";

            Console.WriteLine("With other filters active:");
            Console.WriteLine($"FilterSummary (threshold=300): \"{filterSettings.FilterSummary}\"");

            filterSettings.AdjacencyThreshold = 500.0;
            Console.WriteLine($"FilterSummary (threshold=500): \"{filterSettings.FilterSummary}\"");
            Console.WriteLine();

            Console.WriteLine("Expected format:");
            Console.WriteLine("\"1 Level(s), 1 Category(ies), Failures Only, Search: 'test', Threshold: 500mm\"");
        }

        /// <summary>
        /// Tests input validation scenarios
        /// </summary>
        public static void TestInputValidation()
        {
            Console.WriteLine("=== INPUT VALIDATION TEST ===");
            Console.WriteLine();

            var filterSettings = new FilterSettings();

            var testInputs = new[]
            {
                new { Input = "300", Expected = 300.0, Description = "Valid integer" },
                new { Input = "300.5", Expected = 300.5, Description = "Valid decimal" },
                new { Input = "0", Expected = 1.0, Description = "Below minimum (should clamp to 1)" },
                new { Input = "-50", Expected = 1.0, Description = "Negative (should clamp to 1)" },
                new { Input = "15000", Expected = 10000.0, Description = "Above maximum (should clamp to 10000)" },
                new { Input = "1", Expected = 1.0, Description = "Minimum valid value" },
                new { Input = "10000", Expected = 10000.0, Description = "Maximum valid value" }
            };

            foreach (var test in testInputs)
            {
                filterSettings.Reset();
                
                // Simulate parsing the input (as would happen in TextBox binding)
                if (double.TryParse(test.Input, out double value))
                {
                    filterSettings.AdjacencyThreshold = value;
                }

                Console.WriteLine($"Input: \"{test.Input}\" ({test.Description})");
                Console.WriteLine($"  Parsed value: {value}");
                Console.WriteLine($"  Final AdjacencyThreshold: {filterSettings.AdjacencyThreshold}");
                Console.WriteLine($"  Expected: {test.Expected}");
                Console.WriteLine($"  Result: {(Math.Abs(filterSettings.AdjacencyThreshold - test.Expected) < 0.1 ? "✓ PASS" : "✗ FAIL")}");
                Console.WriteLine();
            }

            Console.WriteLine("UI VALIDATION NOTES:");
            Console.WriteLine("• TextBox PreviewTextInput allows only digits and decimal point");
            Console.WriteLine("• Multiple decimal points are prevented");
            Console.WriteLine("• Value clamping happens in OnAdjacencyThresholdChanged");
            Console.WriteLine("• StringFormat=F0 displays as integer in UI");
        }

        /// <summary>
        /// Runs all adjacency threshold UI tests
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== ADJACENCY THRESHOLD UI TEST SUITE ===");
            Console.WriteLine($"Test run at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            TestAdjacencyThresholdBinding();
            Console.WriteLine(new string('=', 60));
            Console.WriteLine();

            TestFilterSummaryFormat();
            Console.WriteLine(new string('=', 60));
            Console.WriteLine();

            TestThresholdWithOtherFilters();
            Console.WriteLine(new string('=', 60));
            Console.WriteLine();

            TestInputValidation();
            Console.WriteLine();

            Console.WriteLine("=== TEST SUITE COMPLETE ===");
        }
    }
}
