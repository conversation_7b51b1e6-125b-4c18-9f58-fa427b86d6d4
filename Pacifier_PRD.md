## 1. 📌 Purpose

This Revit add-in is built for Passive Fire Engineers to audit and resolve placement issues of fire stopping elements across multiple linked models. It identifies incorrectly placed or broken fire stopping fittings, analyzes their connection to services and structural barriers, performs spatial validation, and exports the results in a structured, engineer-friendly format.

---

## 2. 🧭 Scope

The tool will:

- Extract **Fire Stopping fittings** from MEP links (Ducts, Pipes, Cable Trays)
- Identify and extract **connected service elements**
- Locate adjacent **wall/floor structures** from architectural links
- Detect spatial misalignments and disconnections
- Display grouped, color-coded data in a WPF UI
- Export a schedule to Excel with critical metadata
- Handle linked file transformations and internal origin mismatches robustly

---

## 3. 🔩 Components & Data Requirements

### 1️⃣ Fire Stopping Elements (fittings/accessories)

- **Source**: Duct, Pipe, Cable Tray fittings from linked MEP models
- **Filter**: Family type name contains `"Fire Stopping"`
- **Key Parameters**:
    - ElementId
    - Beca Type Mark
    - Beca Inst Mark
    - Beca System Description
    - Beca Family Material
    - Beca Free Size
    - Beca Family Orientation
    - Family Name
    - Type Name
    - Beca Family Reference

### 2️⃣ Connected Service Elements

- **Source**: Host system element (pipe, duct, cable tray) linked to the fire stopping fitting
- **Key Parameters**:
    - Service Type (e.g., Pipe, Duct, Cable Tray)
    - Size
    - Material
    - System Type

### 3️⃣ Adjacent Structural Elements

- **Source**: Walls and Floors from architectural/interior plan model (linked)
- **Key Parameters**:
    - Structure Type (Wall or Floor)
    - Material Type
    - Fire Rating
    - Thickness

---

## 4. 🌐 Linked Model Context

- All components are **from linked models**, loaded into a **Passive Fire host model**
- Elements exist in **different coordinate systems**
- Fittings may **appear incorrectly placed** due to model shifts or origin differences
- Structural walls/floors may move, breaking valid fire stop conditions
- Tool must resolve these mismatches to ensure spatial accuracy and flag invalid placements

---

## 5. ✅ Design Check Logic  (Critical)

Each Fire Stopping fitting is validated against 4 rules:

| Check Name | Description |
| --- | --- |
| `NotTouchingWall` | No adjacent wall/floor found |
| `NotTouchingService` | Not connected to any service element |
| `Clashing` | Intersects with another fire stopping fitting |
| `Adjacent` | Within 300mm of another fire stopping element |

🔄 All design checks (e.g. `NotTouchingWall`, `NotTouchingService`) will only consider **elements from selected linked models**.

🚦Each Fire Stopping fitting is evaluated against spatial relationships with Services and Structures. These checks are **location-based** and must operate **across linked models with coordinate transformations**.

### 📐 Geometry Matching and Coordinate Resolution (Performance-Critical)

> This is the most computationally expensive and logic-sensitive part of the tool.
> 

**Why it's critical:**

- All elements (Fire Stopping, Services, Walls/Floors) come from **different linked models**
- Linked models may have **different internal origins** or **rotated coordinate systems**
- Misalignment between host and link can lead to incorrect design check outcomes

---

### 🔄 Resolution Strategy:

- Every element from a linked file must be **transformed into the host model coordinate system** using:
    
    ```csharp
    csharp
    CopyEdit
    Transform linkTransform = RevitLinkInstance.GetTransform();
    Solid transformed = SolidUtils.CreateTransformed(originalSolid, linkTransform);
    
    ```
    
- Perform all design checks using this **transformed geometry**

---

### 🚀 Optimization Strategy:

- Use **BoundingBox filtering first** to reduce the search space
- Only compute `Solid.Intersect()` on shortlisted candidates
- Use **spatial grids** or **distance-based filtering** to speed up `Clashing` and `Adjacent` checks
- Cache transformed geometry wherever possible
- Future option: parallelize long-running checks (if thread-safe)

---

## 6. 🧱 Architecture

| Layer | Details |
| --- | --- |
| Context | Revit API ExternalCommand in Passive Fire model |
| Pattern | MVVM (CommunityToolkit.Mvvm) |
| UI Framework | WPF + MaterialDesignThemes |
| Excel Export | ClosedXML |
| DI Support | Yes – via IServiceProvider |
| Testing | Fully testable service layers |
| Extensibility | Parameter fields and checks are designed to evolve |

---

## 7. 🌐 **Linked Model Filtering**

### 🔍 Purpose

Allow users to **select which linked models** to include in the analysis, preventing memory overload and unnecessary intersection checks in large projects.

### 🔧 Feature Details

- Present a list of all **loaded linked models** in the host project
- Allow users to multi-select relevant links (e.g. Electrical, Mechanical, Interior)
- Only elements from the selected links are processed for:
    - Fire Stopping fitting extraction
    - Service and structure detection
    - Intersection evaluation

### 💡 UI Behavior

- Add a **“Linked Models” filter group** in the collapsible sidebar
- Show checkboxes with link names (e.g. `ELC_Link`, `HYD_Model_v2`, `Arch_Internal`)
- Add **tooltips** for model origin or file path (optional)

---

## 8. 🖼 UI Design

### 🔲 Collapsible Sidebar (Left)

- Levels (multi-select checkboxes)
- Categories (Fire Stopping, Pipes, Ducts, etc.)
- “☰” Hamburger toggle
    - Collapsed → icon-only with tooltips
    - Expanded → full label + controls

### 🧾 Main Table View (Right)

Grouped columns for:

- 🟩 Fire Stopping Element Info
- 🟦 Connected Service Info
- 🧱 Adjacent Structure Info
- 🟥 Design Check Results

Highlight failed checks visually

---

## 9. 📤 Excel Export

### Grouped Column Sections:

- **Fire Stopping**: ID, Type Mark, Inst Mark, etc.
- **Service**: Type, Size, Material
- **Structure**: Type, Fire Rating, Thickness
- **Checks**: Boolean columns (Y/N) for each check

### Metadata:

- Project name
- Date/time
- Selected filters
- User or machine ID

## 10. 🚀 Future Enhancements

- Zoom-to-element in Revit
- BIM 360 / ACC issue creation
- 3D section snapshots for issue review
- Native Revit Schedule export
- Custom user-defined rule editor