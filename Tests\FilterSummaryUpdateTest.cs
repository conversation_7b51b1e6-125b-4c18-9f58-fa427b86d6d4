using System;
using System.ComponentModel;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Tests
{
    /// <summary>
    /// Test class to verify FilterSummary updates correctly when filter selections change
    /// </summary>
    public static class FilterSummaryUpdateTest
    {
        /// <summary>
        /// Demonstrates and tests the FilterSummary update behavior
        /// </summary>
        public static void TestFilterSummaryUpdates()
        {
            Console.WriteLine("=== FILTER SUMMARY UPDATE TEST ===");
            Console.WriteLine();

            var filterSettings = new FilterSettings();
            var propertyChangedCount = 0;

            // Subscribe to PropertyChanged to track updates
            filterSettings.PropertyChanged += (sender, e) =>
            {
                if (e.PropertyName == nameof(FilterSettings.FilterSummary))
                {
                    propertyChangedCount++;
                    Console.WriteLine($"FilterSummary updated #{propertyChangedCount}: {filterSettings.FilterSummary}");
                }
            };

            Console.WriteLine($"Initial FilterSummary: {filterSettings.FilterSummary}");
            Console.WriteLine();

            // Test 1: Add some filter items
            Console.WriteLine("TEST 1: Adding filter items...");
            filterSettings.SelectedLevels.Add(new LevelFilter("Level 1", false));
            filterSettings.SelectedLevels.Add(new LevelFilter("Level 2", false));
            filterSettings.SelectedCategories.Add(new CategoryFilter("Duct Fitting", false));
            filterSettings.SelectedLinkedModels.Add(new LinkedModelFilter("Model_MEP.rvt", false));
            Console.WriteLine($"After adding items: {filterSettings.FilterSummary}");
            Console.WriteLine();

            // Test 2: Select some items
            Console.WriteLine("TEST 2: Selecting filter items...");
            filterSettings.SelectedLevels[0].IsSelected = true;
            Console.WriteLine($"After selecting Level 1: {filterSettings.FilterSummary}");

            filterSettings.SelectedCategories[0].IsSelected = true;
            Console.WriteLine($"After selecting Duct Fitting: {filterSettings.FilterSummary}");

            filterSettings.SelectedLinkedModels[0].IsSelected = true;
            Console.WriteLine($"After selecting Model_MEP.rvt: {filterSettings.FilterSummary}");
            Console.WriteLine();

            // Test 3: Enable other filters
            Console.WriteLine("TEST 3: Enabling other filter options...");
            filterSettings.ShowFailuresOnly = true;
            Console.WriteLine($"After enabling ShowFailuresOnly: {filterSettings.FilterSummary}");

            filterSettings.SearchText = "fire stopping";
            Console.WriteLine($"After setting SearchText: {filterSettings.FilterSummary}");
            Console.WriteLine();

            // Test 4: Deselect items
            Console.WriteLine("TEST 4: Deselecting filter items...");
            filterSettings.SelectedLevels[0].IsSelected = false;
            Console.WriteLine($"After deselecting Level 1: {filterSettings.FilterSummary}");

            filterSettings.SelectedCategories[0].IsSelected = false;
            Console.WriteLine($"After deselecting Duct Fitting: {filterSettings.FilterSummary}");
            Console.WriteLine();

            // Test 5: Clear all filters
            Console.WriteLine("TEST 5: Clearing all filters...");
            filterSettings.Reset();
            Console.WriteLine($"After reset: {filterSettings.FilterSummary}");
            Console.WriteLine();

            Console.WriteLine($"Total PropertyChanged events fired: {propertyChangedCount}");
            Console.WriteLine();

            // Verify expected behavior
            Console.WriteLine("EXPECTED BEHAVIOR:");
            Console.WriteLine("✓ FilterSummary should update when items are selected/deselected");
            Console.WriteLine("✓ FilterSummary should show count of SELECTED items, not total items");
            Console.WriteLine("✓ FilterSummary should update when ShowFailuresOnly changes");
            Console.WriteLine("✓ FilterSummary should update when SearchText changes");
            Console.WriteLine("✓ PropertyChanged events should fire for each change");
        }

        /// <summary>
        /// Tests the FilterSummary format and content
        /// </summary>
        public static void TestFilterSummaryFormat()
        {
            Console.WriteLine("=== FILTER SUMMARY FORMAT TEST ===");
            Console.WriteLine();

            var filterSettings = new FilterSettings();

            // Test different combinations
            var testCases = new[]
            {
                new { Description = "No filters", Setup = new Action(() => { /* no setup */ }) },
                new { Description = "1 Level selected", Setup = new Action(() => {
                    filterSettings.SelectedLevels.Add(new LevelFilter("Level 1", true));
                })},
                new { Description = "2 Levels, 1 Category", Setup = new Action(() => {
                    filterSettings.SelectedLevels.Add(new LevelFilter("Level 2", true));
                    filterSettings.SelectedCategories.Add(new CategoryFilter("Pipe Fitting", true));
                })},
                new { Description = "All filter types", Setup = new Action(() => {
                    filterSettings.SelectedLinkedModels.Add(new LinkedModelFilter("Model_ARCH.rvt", true));
                    filterSettings.ShowFailuresOnly = true;
                    filterSettings.SearchText = "test";
                })}
            };

            foreach (var testCase in testCases)
            {
                filterSettings.Reset();
                testCase.Setup();
                Console.WriteLine($"{testCase.Description}: \"{filterSettings.FilterSummary}\"");
            }

            Console.WriteLine();
            Console.WriteLine("EXPECTED FORMATS:");
            Console.WriteLine("• No filters: \"No Filters\"");
            Console.WriteLine("• Single filter: \"1 Level(s)\"");
            Console.WriteLine("• Multiple filters: \"2 Level(s), 1 Category(ies), 1 Linked Model(s), Failures Only, Search: 'test'\"");
        }

        /// <summary>
        /// Tests performance of FilterSummary updates
        /// </summary>
        public static void TestFilterSummaryPerformance()
        {
            Console.WriteLine("=== FILTER SUMMARY PERFORMANCE TEST ===");
            Console.WriteLine();

            var filterSettings = new FilterSettings();
            var updateCount = 0;

            filterSettings.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(FilterSettings.FilterSummary))
                    updateCount++;
            };

            var startTime = DateTime.Now;

            // Add many filter items
            for (int i = 0; i < 100; i++)
            {
                filterSettings.SelectedLevels.Add(new LevelFilter($"Level {i}", i % 3 == 0));
                filterSettings.SelectedCategories.Add(new CategoryFilter($"Category {i}", i % 4 == 0));
                filterSettings.SelectedLinkedModels.Add(new LinkedModelFilter($"Model_{i}.rvt", i % 5 == 0));
            }

            var endTime = DateTime.Now;
            var duration = endTime - startTime;

            Console.WriteLine($"Added 300 filter items in {duration.TotalMilliseconds:F2}ms");
            Console.WriteLine($"FilterSummary updates: {updateCount}");
            Console.WriteLine($"Current FilterSummary: {filterSettings.FilterSummary}");
            Console.WriteLine();

            // Test rapid selection changes
            startTime = DateTime.Now;
            updateCount = 0;

            for (int i = 0; i < 50; i++)
            {
                filterSettings.SelectedLevels[i].IsSelected = !filterSettings.SelectedLevels[i].IsSelected;
            }

            endTime = DateTime.Now;
            duration = endTime - startTime;

            Console.WriteLine($"Changed 50 selections in {duration.TotalMilliseconds:F2}ms");
            Console.WriteLine($"FilterSummary updates: {updateCount}");
            Console.WriteLine($"Final FilterSummary: {filterSettings.FilterSummary}");
            Console.WriteLine();

            Console.WriteLine("PERFORMANCE EXPECTATIONS:");
            Console.WriteLine("✓ Adding items should be fast (< 100ms for 300 items)");
            Console.WriteLine("✓ Selection changes should trigger immediate updates");
            Console.WriteLine("✓ FilterSummary calculation should be efficient");
            Console.WriteLine("✓ No excessive PropertyChanged events");
        }
    }
}
