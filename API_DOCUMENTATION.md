# API Documentation

## Overview

This document provides comprehensive API documentation for the Passive Fire Stopping Solution Exporter. The API is designed around interfaces to promote testability and extensibility.

## Core Interfaces

### IExtractionService

Handles extraction of elements from Revit models with coordinate transformation.

#### Methods

##### ExtractFireStoppingElementsAsync
```csharp
Task<IEnumerable<FireStoppingElement>> ExtractFireStoppingElementsAsync(
    Document document, 
    FilterSettings filterSettings, 
    CancellationToken cancellationToken = default)
```

**Parameters:**
- `document`: Host Revit document
- `filterSettings`: Filter configuration for extraction
- `cancellationToken`: Cancellation token for async operation

**Returns:** Collection of fire stopping elements with transformed geometry

**Throws:**
- `ArgumentNullException`: When document or filterSettings is null
- `OperationCanceledException`: When operation is cancelled
- `InvalidOperationException`: When document validation fails

##### ExtractConnectedServicesAsync
```csharp
Task<IEnumerable<ServiceElement>> ExtractConnectedServicesAsync(
    Document document,
    IEnumerable<FireStoppingElement> fireStoppingElements,
    FilterSettings filterSettings,
    CancellationToken cancellationToken = default)
```

**Parameters:**
- `document`: Host Revit document
- `fireStoppingElements`: Fire stopping elements to find connections for
- `filterSettings`: Filter configuration
- `cancellationToken`: Cancellation token

**Returns:** Collection of connected service elements

##### ExtractStructuralElementsAsync
```csharp
Task<IEnumerable<StructuralElement>> ExtractStructuralElementsAsync(
    Document document,
    FilterSettings filterSettings,
    CancellationToken cancellationToken = default)
```

**Parameters:**
- `document`: Host Revit document
- `filterSettings`: Filter configuration
- `cancellationToken`: Cancellation token

**Returns:** Collection of structural elements (walls, floors)

#### Events

##### ProgressChanged
```csharp
event EventHandler<ExtractionProgressEventArgs> ProgressChanged
```

Raised when extraction progress changes.

##### StatusChanged
```csharp
event EventHandler<ExtractionStatusEventArgs> StatusChanged
```

Raised when extraction status changes.

### IDesignCheckService

Performs spatial validation and design checks on fire stopping elements.

#### Methods

##### PerformDesignChecksAsync
```csharp
Task<IEnumerable<FireStoppingElement>> PerformDesignChecksAsync(
    IEnumerable<FireStoppingElement> fireStoppingElements,
    IEnumerable<ServiceElement> serviceElements,
    IEnumerable<StructuralElement> structuralElements,
    FilterSettings filterSettings,
    CancellationToken cancellationToken = default)
```

**Parameters:**
- `fireStoppingElements`: Elements to check
- `serviceElements`: Available service elements
- `structuralElements`: Available structural elements
- `filterSettings`: Check configuration
- `cancellationToken`: Cancellation token

**Returns:** Fire stopping elements with updated check results

##### CheckNotTouchingWallAsync
```csharp
Task<bool> CheckNotTouchingWallAsync(
    FireStoppingElement fireStoppingElement,
    IEnumerable<StructuralElement> structuralElements)
```

**Parameters:**
- `fireStoppingElement`: Element to check
- `structuralElements`: Available structural elements

**Returns:** True if element is not touching any wall/floor (failure condition)

##### CheckClashingAsync
```csharp
Task<(bool IsClashing, IEnumerable<ElementId> ClashingElements)> CheckClashingAsync(
    FireStoppingElement fireStoppingElement,
    IEnumerable<FireStoppingElement> otherFireStoppingElements)
```

**Parameters:**
- `fireStoppingElement`: Element to check
- `otherFireStoppingElements`: Other fire stopping elements

**Returns:** Tuple indicating if clashing and list of clashing element IDs

### IExcelExportService

Handles Excel export functionality with comprehensive formatting.

#### Methods

##### ExportToExcelAsync
```csharp
Task<ExportResult> ExportToExcelAsync(
    IEnumerable<FireStoppingElement> fireStoppingElements,
    FilterSettings filterSettings,
    string filePath,
    ExportSettings exportSettings,
    CancellationToken cancellationToken = default)
```

**Parameters:**
- `fireStoppingElements`: Elements to export
- `filterSettings`: Applied filters for metadata
- `filePath`: Output file path
- `exportSettings`: Export configuration
- `cancellationToken`: Cancellation token

**Returns:** ExportResult with success status and details

##### CreateExportPreview
```csharp
ExportPreview CreateExportPreview(
    IEnumerable<FireStoppingElement> fireStoppingElements,
    ExportSettings exportSettings)
```

**Parameters:**
- `fireStoppingElements`: Elements to preview
- `exportSettings`: Export configuration

**Returns:** Preview information including estimated file size and structure

### IGeometryHelper

Critical component for spatial operations and coordinate transformations.

#### Methods

##### TransformElementGeometry
```csharp
void TransformElementGeometry(
    Element element, 
    Transform linkTransform, 
    FireStoppingElement targetElement)
```

**Parameters:**
- `element`: Source Revit element
- `linkTransform`: Transform from link instance
- `targetElement`: Target element to update

**Purpose:** Transforms geometry from linked model coordinates to host coordinates

##### DoSolidsIntersect
```csharp
bool DoSolidsIntersect(Solid solid1, Solid solid2)
```

**Parameters:**
- `solid1`: First solid
- `solid2`: Second solid

**Returns:** True if solids intersect

**Performance:** Uses BooleanOperationsUtils for accurate intersection detection

##### CalculateDistance
```csharp
double CalculateDistance(XYZ point1, XYZ point2)
```

**Parameters:**
- `point1`: First point
- `point2`: Second point

**Returns:** Distance in feet (Revit internal units)

### ISpatialHelper

Performance optimization through spatial indexing.

#### Methods

##### CreateSpatialIndex
```csharp
SpatialIndex CreateSpatialIndex(
    IEnumerable<FireStoppingElement> fireStoppingElements,
    IEnumerable<StructuralElement> structuralElements,
    IEnumerable<ServiceElement> serviceElements)
```

**Parameters:**
- `fireStoppingElements`: Fire stopping elements to index
- `structuralElements`: Structural elements to index
- `serviceElements`: Service elements to index

**Returns:** Spatial index for efficient proximity searches

##### FindNearbyElements
```csharp
SpatialSearchResult FindNearbyElements(
    SpatialIndex spatialIndex,
    FireStoppingElement targetElement,
    double searchDistance)
```

**Parameters:**
- `spatialIndex`: Pre-built spatial index
- `targetElement`: Target element for search
- `searchDistance`: Search radius in feet

**Returns:** Nearby elements within search distance

## Data Models

### FireStoppingElement

Primary data model representing a fire stopping fitting.

#### Key Properties
```csharp
public ElementId ElementId { get; set; }
public string BecaTypeMark { get; set; }
public string BecaInstMark { get; set; }
public string FamilyName { get; set; }
public XYZ LocationPoint { get; set; }
public Solid TransformedSolid { get; set; }
public ServiceElement? ConnectedService { get; set; }
public StructuralElement? AdjacentStructure { get; set; }
public DesignCheckResult DesignCheckResult { get; set; }
```

#### Methods
```csharp
public bool IsValid()                    // Validates required parameters
public FireStoppingElement Clone()       // Creates deep copy
public void SetCustomParameter(string name, object value)  // Extensibility
```

### DesignCheckResult

Contains results of all design checks performed on an element.

#### Properties
```csharp
public bool NotTouchingWall { get; set; }
public bool NotTouchingService { get; set; }
public bool Clashing { get; set; }
public bool Adjacent { get; set; }
public bool HasFailures => /* any check failed */
public int FailureCount => /* count of failed checks */
public string FailureSummary => /* formatted summary */
```

#### Methods
```csharp
public void SetCheckMessage(string checkType, string message)
public void AddCustomCheck(string checkName, bool failed, string message = "")
public void Reset()  // Resets all checks to passing state
```

### FilterSettings

Configuration for filtering and analysis parameters.

#### Properties
```csharp
public ObservableCollection<LevelFilter> SelectedLevels { get; set; }
public ObservableCollection<CategoryFilter> SelectedCategories { get; set; }
public ObservableCollection<LinkedModelFilter> SelectedLinkedModels { get; set; }
public bool ShowFailuresOnly { get; set; }
public double AdjacencyThreshold { get; set; } = 300.0; // mm
public string SearchText { get; set; }
```

### ExportSettings

Configuration for Excel export behavior.

#### Properties
```csharp
public bool IncludeFireStoppingDetails { get; set; } = true;
public bool IncludeServiceDetails { get; set; } = true;
public bool IncludeStructureDetails { get; set; } = true;
public bool IncludeDesignChecks { get; set; } = true;
public bool ApplyFormatting { get; set; } = true;
public string ProjectName { get; set; }
public string UserName { get; set; }
```

## Event Arguments

### ExtractionProgressEventArgs
```csharp
public class ExtractionProgressEventArgs : EventArgs
{
    public int Current { get; set; }
    public int Total { get; set; }
    public string CurrentOperation { get; set; }
    public double PercentComplete => Total > 0 ? (double)Current / Total * 100 : 0;
}
```

### DesignCheckProgressEventArgs
```csharp
public class DesignCheckProgressEventArgs : EventArgs
{
    public int Current { get; set; }
    public int Total { get; set; }
    public string CurrentOperation { get; set; }
    public string ElementId { get; set; }
    public double PercentComplete => Total > 0 ? (double)Current / Total * 100 : 0;
}
```

## Error Handling

### Common Exceptions

#### ExtractionService
- `ArgumentNullException`: Invalid input parameters
- `InvalidOperationException`: Document validation failures
- `UnauthorizedAccessException`: Insufficient permissions for linked models

#### DesignCheckService
- `InvalidOperationException`: Invalid geometry for intersection checks
- `OutOfMemoryException`: Large dataset processing issues

#### ExcelExportService
- `DirectoryNotFoundException`: Invalid export path
- `UnauthorizedAccessException`: File permission issues
- `InvalidOperationException`: Export configuration errors

### Best Practices

1. **Always use try-catch blocks** when calling service methods
2. **Check cancellation tokens** in long-running operations
3. **Validate inputs** before calling API methods
4. **Handle progress events** for user feedback
5. **Dispose resources** properly after use

## Usage Examples

### Basic Extraction
```csharp
var extractionService = ServiceContainer.GetService<IExtractionService>();
var filterSettings = new FilterSettings();

var elements = await extractionService.ExtractFireStoppingElementsAsync(
    document, filterSettings, cancellationToken);
```

### Design Checks
```csharp
var designCheckService = ServiceContainer.GetService<IDesignCheckService>();

var checkedElements = await designCheckService.PerformDesignChecksAsync(
    fireStoppingElements, serviceElements, structuralElements, 
    filterSettings, cancellationToken);
```

### Excel Export
```csharp
var exportService = ServiceContainer.GetService<IExcelExportService>();
var exportSettings = exportService.GetDefaultExportSettings();

var result = await exportService.ExportToExcelAsync(
    elements, filterSettings, filePath, exportSettings, cancellationToken);
```

This API documentation provides the foundation for understanding and extending the Fire Stopping Solution Exporter functionality.
