<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Command">
    <Text>Passive Fire Stopping Solution Exporter</Text>
    <Description>
      A comprehensive Revit add-in for auditing and resolving placement issues of fire stopping elements across multiple linked models. 
      Built for Passive Fire Engineers to identify incorrectly placed or broken fire stopping fittings, analyze their connection to services 
      and structural barriers, perform spatial validation, and export results in a structured, engineer-friendly format.
    </Description>
    <Assembly>MEP.Pacifire.dll</Assembly>
    <FullClassName>MEP.Pacifire.RevitCommands.PacifireCommand</FullClassName>
    <ClientId>A1B2C3D4-E5F6-7890-ABCD-EF1234567890</ClientId>
    <VendorId>BECA</VendorId>
    <VendorDescription>BECA - Building Engineering Consultants</VendorDescription>
    <AvailabilityClassName>MEP.Pacifire.RevitCommands.PacifireAvailability</AvailabilityClassName>
    <LargeImage>Resources\PacifireLarge.png</LargeImage>
    <SmallImage>Resources\PacifireSmall.png</SmallImage>
    <LongDescription>
      The Passive Fire Stopping Solution Exporter provides comprehensive analysis and reporting capabilities for fire stopping elements:

      Key Features:
      • Multi-Model Analysis: Extract fire stopping fittings from MEP links (Ducts, Pipes, Cable Trays)
      • Service Detection: Identify and extract connected service elements
      • Structural Analysis: Locate adjacent wall/floor structures from architectural links
      • Spatial Validation: Detect misalignments and disconnections with accurate coordinate transformation
      • Design Checks: Perform four critical validation checks on each element
      • Excel Export: Generate structured schedules with critical metadata

      Design Checks:
      1. NotTouchingWall: Fire stopping element is not adjacent to any wall or floor
      2. NotTouchingService: Fire stopping element is not connected to any service element
      3. Clashing: Fire stopping element intersects with another fire stopping element
      4. Adjacent: Fire stopping element is within 300mm of another fire stopping element

      User Interface:
      • Collapsible Sidebar: Filter controls with hamburger menu toggle
      • Material Design: Modern WPF interface using MaterialDesignThemes
      • Real-time Filtering: Level, category, and linked model filters
      • Progress Tracking: Real-time progress updates during analysis
      • Summary Dashboard: Quick overview of analysis results

      Technical Features:
      • MVVM Architecture: Clean separation of concerns using CommunityToolkit.Mvvm
      • Dependency Injection: Microsoft.Extensions.DependencyInjection for testability
      • Spatial Indexing: Performance-optimized proximity searches for large datasets
      • Coordinate Transformation: Accurate geometry transformation between linked models
      • Excel Export: ClosedXML-based export with grouped sections and formatting

      Requirements:
      • Autodesk Revit 2020-2026
      • .NET 6.0 or later
      • Windows 10/11
      • Linked MEP and Architectural models for full functionality

      Developed by Tristan Balme and Firza Utama for BECA.
    </LongDescription>
  </AddIn>
</RevitAddIns>
