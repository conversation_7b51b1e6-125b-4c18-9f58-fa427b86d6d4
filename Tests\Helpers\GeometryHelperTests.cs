using System;
using Xunit;
using FluentAssertions;
using Moq;
using Autodesk.Revit.DB;
using MEP.Pacifire.Helpers;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Tests.Helpers
{
    /// <summary>
    /// Unit tests for GeometryHelper class.
    /// Demonstrates testing approach for geometry operations and coordinate transformations.
    /// </summary>
    public class GeometryHelperTests
    {
        private readonly GeometryHelper _geometryHelper;

        public GeometryHelperTests()
        {
            _geometryHelper = new GeometryHelper();
        }

        [Fact]
        public void CalculateDistance_WithValidPoints_ReturnsCorrectDistance()
        {
            // Arrange
            var point1 = new XYZ(0, 0, 0);
            var point2 = new XYZ(3, 4, 0); // 3-4-5 triangle

            // Act
            var distance = _geometryHelper.CalculateDistance(point1, point2);

            // Assert
            distance.Should().BeApproximately(5.0, 0.001);
        }

        [Fact]
        public void CalculateDistance_WithNullPoints_ReturnsMaxValue()
        {
            // Arrange
            XYZ? point1 = null;
            var point2 = new XYZ(1, 1, 1);

            // Act
            var distance = _geometryHelper.CalculateDistance(point1, point2);

            // Assert
            distance.Should().Be(double.MaxValue);
        }

        [Fact]
        public void FeetToMillimeters_WithValidInput_ReturnsCorrectConversion()
        {
            // Arrange
            var feet = 1.0;

            // Act
            var millimeters = _geometryHelper.FeetToMillimeters(feet);

            // Assert
            millimeters.Should().BeApproximately(304.8, 0.1);
        }

        [Fact]
        public void MillimetersToFeet_WithValidInput_ReturnsCorrectConversion()
        {
            // Arrange
            var millimeters = 304.8;

            // Act
            var feet = _geometryHelper.MillimetersToFeet(millimeters);

            // Assert
            feet.Should().BeApproximately(1.0, 0.001);
        }

        [Fact]
        public void GetBoundingBoxCenter_WithValidBoundingBox_ReturnsCenter()
        {
            // Arrange
            var boundingBox = new BoundingBoxXYZ
            {
                Min = new XYZ(0, 0, 0),
                Max = new XYZ(10, 20, 30)
            };

            // Act
            var center = _geometryHelper.GetBoundingBoxCenter(boundingBox);

            // Assert
            center.X.Should().BeApproximately(5.0, 0.001);
            center.Y.Should().BeApproximately(10.0, 0.001);
            center.Z.Should().BeApproximately(15.0, 0.001);
        }

        [Fact]
        public void GetBoundingBoxCenter_WithNullBoundingBox_ReturnsZero()
        {
            // Arrange
            BoundingBoxXYZ? boundingBox = null;

            // Act
            var center = _geometryHelper.GetBoundingBoxCenter(boundingBox);

            // Assert
            center.Should().Be(XYZ.Zero);
        }

        [Fact]
        public void GetBoundingBoxCenter_WithNullUVBoundingBox_ReturnsZero()
        {
            // Arrange
            BoundingBoxUV? boundingBox = null;
            var mockFace = new Mock<Face>();

            // Act
            var center = _geometryHelper.GetBoundingBoxCenter(boundingBox, mockFace.Object);

            // Assert
            center.Should().Be(XYZ.Zero);
        }

        [Fact]
        public void GetBoundingBoxCenter_WithNullFace_ReturnsZero()
        {
            // Arrange
            var boundingBox = new BoundingBoxUV(0, 0, 1, 1);
            Face? face = null;

            // Act
            var center = _geometryHelper.GetBoundingBoxCenter(boundingBox, face);

            // Assert
            center.Should().Be(XYZ.Zero);
        }

        // Note: Testing with actual Face objects requires more complex setup
        // as Face is an abstract Revit API class that cannot be easily mocked.
        // In a real test environment, you would need to create test fixtures
        // with actual Revit geometry or use integration tests.


        [Fact]
        public void DoBoundingBoxesIntersect_WithIntersectingBoxes_ReturnsTrue()
        {
            // Arrange
            var box1 = new BoundingBoxXYZ
            {
                Min = new XYZ(0, 0, 0),
                Max = new XYZ(10, 10, 10)
            };
            var box2 = new BoundingBoxXYZ
            {
                Min = new XYZ(5, 5, 5),
                Max = new XYZ(15, 15, 15)
            };

            // Act
            var intersects = _geometryHelper.DoBoundingBoxesIntersect(box1, box2);

            // Assert
            intersects.Should().BeTrue();
        }

        [Fact]
        public void DoBoundingBoxesIntersect_WithNonIntersectingBoxes_ReturnsFalse()
        {
            // Arrange
            var box1 = new BoundingBoxXYZ
            {
                Min = new XYZ(0, 0, 0),
                Max = new XYZ(5, 5, 5)
            };
            var box2 = new BoundingBoxXYZ
            {
                Min = new XYZ(10, 10, 10),
                Max = new XYZ(15, 15, 15)
            };

            // Act
            var intersects = _geometryHelper.DoBoundingBoxesIntersect(box1, box2);

            // Assert
            intersects.Should().BeFalse();
        }

        [Fact]
        public void ExpandBoundingBox_WithValidInput_ReturnsExpandedBox()
        {
            // Arrange
            var originalBox = new BoundingBoxXYZ
            {
                Min = new XYZ(5, 5, 5),
                Max = new XYZ(10, 10, 10)
            };
            var expansion = 2.0;

            // Act
            var expandedBox = _geometryHelper.ExpandBoundingBox(originalBox, expansion);

            // Assert
            expandedBox.Should().NotBeNull();
            expandedBox.Min.X.Should().BeApproximately(3.0, 0.001);
            expandedBox.Min.Y.Should().BeApproximately(3.0, 0.001);
            expandedBox.Min.Z.Should().BeApproximately(3.0, 0.001);
            expandedBox.Max.X.Should().BeApproximately(12.0, 0.001);
            expandedBox.Max.Y.Should().BeApproximately(12.0, 0.001);
            expandedBox.Max.Z.Should().BeApproximately(12.0, 0.001);
        }

        [Fact]
        public void TransformPoint_WithIdentityTransform_ReturnsSamePoint()
        {
            // Arrange
            var originalPoint = new XYZ(1, 2, 3);
            var identityTransform = Transform.Identity;

            // Act
            var transformedPoint = _geometryHelper.TransformPoint(originalPoint, identityTransform);

            // Assert
            transformedPoint.X.Should().BeApproximately(originalPoint.X, 0.001);
            transformedPoint.Y.Should().BeApproximately(originalPoint.Y, 0.001);
            transformedPoint.Z.Should().BeApproximately(originalPoint.Z, 0.001);
        }

        [Theory]
        [InlineData(0.0, 0.0)]
        [InlineData(1.0, 304.8)]
        [InlineData(10.0, 3048.0)]
        [InlineData(-1.0, -304.8)]
        public void FeetToMillimeters_WithVariousInputs_ReturnsCorrectValues(double feet, double expectedMm)
        {
            // Act
            var result = _geometryHelper.FeetToMillimeters(feet);

            // Assert
            result.Should().BeApproximately(expectedMm, 0.1);
        }

        [Theory]
        [InlineData(0.0, 0.0)]
        [InlineData(304.8, 1.0)]
        [InlineData(3048.0, 10.0)]
        [InlineData(-304.8, -1.0)]
        public void MillimetersToFeet_WithVariousInputs_ReturnsCorrectValues(double mm, double expectedFeet)
        {
            // Act
            var result = _geometryHelper.MillimetersToFeet(mm);

            // Assert
            result.Should().BeApproximately(expectedFeet, 0.001);
        }

        [Fact]
        public void IsValidSolid_WithNullSolid_ReturnsFalse()
        {
            // Arrange
            Solid? solid = null;

            // Act
            var isValid = _geometryHelper.IsValidSolid(solid);

            // Assert
            isValid.Should().BeFalse();
        }

        // Note: Testing with actual Solid objects requires more complex setup
        // as Solid is a Revit API class that cannot be easily mocked.
        // In a real test environment, you would need to create test fixtures
        // with actual Revit geometry or use integration tests.

        [Fact]
        public void CalculateBoundingBoxDistance_WithSeparateBoxes_ReturnsDistance()
        {
            // Arrange
            var box1 = new BoundingBoxXYZ
            {
                Min = new XYZ(0, 0, 0),
                Max = new XYZ(1, 1, 1)
            };
            var box2 = new BoundingBoxXYZ
            {
                Min = new XYZ(5, 5, 5),
                Max = new XYZ(6, 6, 6)
            };

            // Act
            var distance = _geometryHelper.CalculateBoundingBoxDistance(box1, box2);

            // Assert
            // Distance between centers: (0.5,0.5,0.5) to (5.5,5.5,5.5)
            // = sqrt(5^2 + 5^2 + 5^2) = sqrt(75) ≈ 8.66
            distance.Should().BeApproximately(8.66, 0.1);
        }

        [Fact]
        public void CalculateBoundingBoxDistance_WithIntersectingBoxes_ReturnsZero()
        {
            // Arrange
            var box1 = new BoundingBoxXYZ
            {
                Min = new XYZ(0, 0, 0),
                Max = new XYZ(10, 10, 10)
            };
            var box2 = new BoundingBoxXYZ
            {
                Min = new XYZ(5, 5, 5),
                Max = new XYZ(15, 15, 15)
            };

            // Act
            var distance = _geometryHelper.CalculateBoundingBoxDistance(box1, box2);

            // Assert
            distance.Should().Be(0.0);
        }
    }
}
