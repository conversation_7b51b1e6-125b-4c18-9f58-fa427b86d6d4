using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Utility class for analyzing and comparing performance of different adjacency check implementations
    /// </summary>
    public static class PerformanceAnalyzer
    {
        /// <summary>
        /// Performance metrics for adjacency check operations
        /// </summary>
        public class PerformanceMetrics
        {
            public string MethodName { get; set; } = string.Empty;
            public TimeSpan ExecutionTime { get; set; }
            public int ElementsProcessed { get; set; }
            public int DistanceCalculations { get; set; }
            public int CacheHits { get; set; }
            public int CacheMisses { get; set; }
            public long MemoryUsed { get; set; }
            public double AverageTimePerElement => ElementsProcessed > 0 ? ExecutionTime.TotalMilliseconds / ElementsProcessed : 0;
            public double CacheHitRatio => (CacheHits + CacheMisses) > 0 ? (double)CacheHits / (CacheHits + CacheMisses) : 0;
        }

        /// <summary>
        /// Compares performance between legacy and optimized adjacency check methods
        /// </summary>
        public static PerformanceComparison CompareAdjacencyMethods(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            IDesignCheckService designCheckService,
            ISpatialHelper spatialHelper,
            double adjacencyThreshold = 300.0)
        {
            var elementsList = fireStoppingElements.ToList();
            var comparison = new PerformanceComparison();

            // Test legacy method
            comparison.LegacyMetrics = MeasureLegacyMethod(elementsList, designCheckService, adjacencyThreshold);

            // Test optimized method
            comparison.OptimizedMetrics = MeasureOptimizedMethod(elementsList, designCheckService, spatialHelper, adjacencyThreshold);

            // Calculate improvement ratios
            comparison.SpeedImprovement = comparison.LegacyMetrics.ExecutionTime.TotalMilliseconds / 
                                        comparison.OptimizedMetrics.ExecutionTime.TotalMilliseconds;
            comparison.MemoryImprovement = (double)comparison.LegacyMetrics.MemoryUsed / 
                                         comparison.OptimizedMetrics.MemoryUsed;

            return comparison;
        }

        /// <summary>
        /// Measures performance of the legacy adjacency check method
        /// </summary>
        private static PerformanceMetrics MeasureLegacyMethod(
            List<FireStoppingElement> elements,
            IDesignCheckService designCheckService,
            double adjacencyThreshold)
        {
            var metrics = new PerformanceMetrics { MethodName = "Legacy CheckAdjacent" };
            var stopwatch = Stopwatch.StartNew();
            var initialMemory = GC.GetTotalMemory(true);

            int distanceCalculations = 0;
            foreach (var element in elements)
            {
                var result = designCheckService.CheckAdjacent(element, elements, adjacencyThreshold);
                distanceCalculations += elements.Count - 1; // Approximate
            }

            stopwatch.Stop();
            var finalMemory = GC.GetTotalMemory(false);

            metrics.ExecutionTime = stopwatch.Elapsed;
            metrics.ElementsProcessed = elements.Count;
            metrics.DistanceCalculations = distanceCalculations;
            metrics.MemoryUsed = finalMemory - initialMemory;

            return metrics;
        }

        /// <summary>
        /// Measures performance of the optimized adjacency check method
        /// </summary>
        private static PerformanceMetrics MeasureOptimizedMethod(
            List<FireStoppingElement> elements,
            IDesignCheckService designCheckService,
            ISpatialHelper spatialHelper,
            double adjacencyThreshold)
        {
            var metrics = new PerformanceMetrics { MethodName = "Optimized CheckAdjacent" };
            var stopwatch = Stopwatch.StartNew();
            var initialMemory = GC.GetTotalMemory(true);

            // Create spatial index
            var spatialIndex = spatialHelper.CreateSpatialIndex(elements, Enumerable.Empty<StructuralElement>(), Enumerable.Empty<ServiceElement>());

            // Get initial cache statistics
            var initialCacheStats = SpatialHelper.GetCacheStatistics();

            int distanceCalculations = 0;
            foreach (var element in elements)
            {
                var result = designCheckService.CheckAdjacentOptimized(element, spatialIndex, adjacencyThreshold);
                // Distance calculations are reduced due to spatial indexing
            }

            stopwatch.Stop();
            var finalMemory = GC.GetTotalMemory(false);
            var finalCacheStats = SpatialHelper.GetCacheStatistics();

            metrics.ExecutionTime = stopwatch.Elapsed;
            metrics.ElementsProcessed = elements.Count;
            metrics.CacheHits = finalCacheStats.Count - initialCacheStats.Count;
            metrics.MemoryUsed = finalMemory - initialMemory;

            return metrics;
        }

        /// <summary>
        /// Generates a performance report
        /// </summary>
        public static string GeneratePerformanceReport(PerformanceComparison comparison)
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== ADJACENCY CHECK PERFORMANCE ANALYSIS ===");
            report.AppendLine();

            // Legacy method stats
            report.AppendLine($"Legacy Method ({comparison.LegacyMetrics.MethodName}):");
            report.AppendLine($"  Execution Time: {comparison.LegacyMetrics.ExecutionTime.TotalMilliseconds:F2} ms");
            report.AppendLine($"  Elements Processed: {comparison.LegacyMetrics.ElementsProcessed:N0}");
            report.AppendLine($"  Distance Calculations: {comparison.LegacyMetrics.DistanceCalculations:N0}");
            report.AppendLine($"  Average Time/Element: {comparison.LegacyMetrics.AverageTimePerElement:F2} ms");
            report.AppendLine($"  Memory Used: {comparison.LegacyMetrics.MemoryUsed / 1024.0:F2} KB");
            report.AppendLine();

            // Optimized method stats
            report.AppendLine($"Optimized Method ({comparison.OptimizedMetrics.MethodName}):");
            report.AppendLine($"  Execution Time: {comparison.OptimizedMetrics.ExecutionTime.TotalMilliseconds:F2} ms");
            report.AppendLine($"  Elements Processed: {comparison.OptimizedMetrics.ElementsProcessed:N0}");
            report.AppendLine($"  Cache Hits: {comparison.OptimizedMetrics.CacheHits:N0}");
            report.AppendLine($"  Cache Hit Ratio: {comparison.OptimizedMetrics.CacheHitRatio:P2}");
            report.AppendLine($"  Average Time/Element: {comparison.OptimizedMetrics.AverageTimePerElement:F2} ms");
            report.AppendLine($"  Memory Used: {comparison.OptimizedMetrics.MemoryUsed / 1024.0:F2} KB");
            report.AppendLine();

            // Improvement summary
            report.AppendLine("Performance Improvements:");
            report.AppendLine($"  Speed Improvement: {comparison.SpeedImprovement:F1}x faster");
            report.AppendLine($"  Memory Improvement: {comparison.MemoryImprovement:F1}x less memory");
            report.AppendLine($"  Time Saved: {comparison.LegacyMetrics.ExecutionTime.TotalMilliseconds - comparison.OptimizedMetrics.ExecutionTime.TotalMilliseconds:F2} ms");
            
            var percentImprovement = ((comparison.LegacyMetrics.ExecutionTime.TotalMilliseconds - comparison.OptimizedMetrics.ExecutionTime.TotalMilliseconds) / 
                                    comparison.LegacyMetrics.ExecutionTime.TotalMilliseconds) * 100;
            report.AppendLine($"  Percentage Improvement: {percentImprovement:F1}%");

            return report.ToString();
        }

        /// <summary>
        /// Estimates performance for different dataset sizes
        /// </summary>
        public static string EstimateScalability(PerformanceComparison baseComparison, int[] elementCounts)
        {
            var report = new System.Text.StringBuilder();
            report.AppendLine("=== SCALABILITY ANALYSIS ===");
            report.AppendLine();
            report.AppendLine("Estimated performance for different dataset sizes:");
            report.AppendLine("Elements | Legacy Time | Optimized Time | Improvement");
            report.AppendLine("---------|-------------|----------------|------------");

            var baseElements = baseComparison.LegacyMetrics.ElementsProcessed;
            var baseLegacyTime = baseComparison.LegacyMetrics.ExecutionTime.TotalSeconds;
            var baseOptimizedTime = baseComparison.OptimizedMetrics.ExecutionTime.TotalSeconds;

            foreach (var count in elementCounts)
            {
                // Legacy scales O(n²)
                var legacyTime = baseLegacyTime * Math.Pow((double)count / baseElements, 2);
                
                // Optimized scales approximately O(n×log(n)) due to spatial indexing
                var optimizedTime = baseOptimizedTime * count * Math.Log(count) / (baseElements * Math.Log(baseElements));

                var improvement = legacyTime / optimizedTime;

                report.AppendLine($"{count,8:N0} | {legacyTime,10:F2}s | {optimizedTime,13:F2}s | {improvement,9:F1}x");
            }

            return report.ToString();
        }
    }

    /// <summary>
    /// Contains comparison results between legacy and optimized methods
    /// </summary>
    public class PerformanceComparison
    {
        public PerformanceAnalyzer.PerformanceMetrics LegacyMetrics { get; set; } = new();
        public PerformanceAnalyzer.PerformanceMetrics OptimizedMetrics { get; set; } = new();
        public double SpeedImprovement { get; set; }
        public double MemoryImprovement { get; set; }
    }
}
