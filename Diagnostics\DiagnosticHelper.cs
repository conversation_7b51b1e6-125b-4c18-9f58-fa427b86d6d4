using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using MEP.Pacifire.Configuration;
using MEP.Pacifire.Helpers;
using MEP.Pacifire.Services;

namespace MEP.Pacifire.Diagnostics
{
    /// <summary>
    /// Diagnostic helper for troubleshooting and system information collection.
    /// Provides comprehensive system diagnostics for support and debugging.
    /// </summary>
    public static class DiagnosticHelper
    {
        /// <summary>
        /// Generates a comprehensive diagnostic report
        /// </summary>
        /// <param name="document">Optional Revit document for model-specific diagnostics</param>
        /// <returns>Diagnostic report as formatted string</returns>
        public static async Task<string> GenerateDiagnosticReportAsync(Document? document = null)
        {
            var report = new StringBuilder();
            var logger = Logger.Instance;

            try
            {
                logger.Information("Generating diagnostic report");

                report.AppendLine("=".PadRight(80, '='));
                report.AppendLine("PASSIVE FIRE STOPPING SOLUTION EXPORTER - DIAGNOSTIC REPORT");
                report.AppendLine("=".PadRight(80, '='));
                report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine();

                // System Information
                AppendSystemInformation(report);

                // Application Information
                AppendApplicationInformation(report);

                // Revit Information
                if (document != null)
                {
                    AppendRevitInformation(report, document);
                }

                // Service Container Status
                AppendServiceContainerStatus(report);

                // Configuration Status
                AppendConfigurationStatus(report);

                // Performance Metrics
                await AppendPerformanceMetricsAsync(report);

                // Log Statistics
                AppendLogStatistics(report);

                // Recent Errors
                await AppendRecentErrorsAsync(report);

                report.AppendLine("=".PadRight(80, '='));
                report.AppendLine("END OF DIAGNOSTIC REPORT");
                report.AppendLine("=".PadRight(80, '='));

                logger.Information("Diagnostic report generated successfully");
            }
            catch (Exception ex)
            {
                logger.Error("Error generating diagnostic report", ex);
                report.AppendLine($"ERROR GENERATING REPORT: {ex.Message}");
            }

            return report.ToString();
        }

        /// <summary>
        /// Validates system requirements and configuration
        /// </summary>
        /// <returns>Validation result with issues and recommendations</returns>
        public static SystemValidationResult ValidateSystem()
        {
            var result = new SystemValidationResult();
            var logger = Logger.Instance;

            try
            {
                logger.Information("Starting system validation");

                // Check .NET version
                ValidateDotNetVersion(result);

                // Check available memory
                ValidateMemory(result);

                // Check disk space
                ValidateDiskSpace(result);

                // Check dependencies
                ValidateDependencies(result);

                // Check permissions
                ValidatePermissions(result);

                // Check service container
                ValidateServiceContainer(result);

                result.IsValid = result.CriticalIssues.Count == 0;
                
                logger.Information($"System validation completed. Valid: {result.IsValid}, Issues: {result.CriticalIssues.Count}, Warnings: {result.Warnings.Count}");
            }
            catch (Exception ex)
            {
                logger.Error("Error during system validation", ex);
                result.CriticalIssues.Add($"Validation error: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// Collects performance metrics
        /// </summary>
        /// <returns>Performance metrics</returns>
        public static PerformanceMetrics CollectPerformanceMetrics()
        {
            var process = Process.GetCurrentProcess();
            var gcInfo = GC.GetTotalMemory(false);

            return new PerformanceMetrics
            {
                WorkingSetMemoryMB = process.WorkingSet64 / (1024 * 1024),
                PrivateMemoryMB = process.PrivateMemorySize64 / (1024 * 1024),
                ManagedMemoryMB = gcInfo / (1024 * 1024),
                ProcessorTimeMs = (long)process.TotalProcessorTime.TotalMilliseconds,
                ThreadCount = process.Threads.Count,
                HandleCount = process.HandleCount,
                GCGen0Collections = GC.CollectionCount(0),
                GCGen1Collections = GC.CollectionCount(1),
                GCGen2Collections = GC.CollectionCount(2),
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// Exports diagnostic information to file
        /// </summary>
        /// <param name="filePath">Output file path</param>
        /// <param name="document">Optional Revit document</param>
        /// <returns>True if export successful</returns>
        public static async Task<bool> ExportDiagnosticsAsync(string filePath, Document? document = null)
        {
            try
            {
                var report = await GenerateDiagnosticReportAsync(document);
                await FileHelper.WriteAllTextAsync(filePath, report);

                Logger.Instance.Information($"Diagnostic report exported to: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error exporting diagnostics to {filePath}", ex);
                return false;
            }
        }

        #region Private Helper Methods

        private static void AppendSystemInformation(StringBuilder report)
        {
            report.AppendLine("SYSTEM INFORMATION");
            report.AppendLine("-".PadRight(40, '-'));
            report.AppendLine($"OS: {Environment.OSVersion}");
            report.AppendLine($"Architecture: {RuntimeInformation.OSArchitecture}");
            report.AppendLine($"Framework: {RuntimeInformation.FrameworkDescription}");
            report.AppendLine($"Machine Name: {Environment.MachineName}");
            report.AppendLine($"User: {Environment.UserName}");
            report.AppendLine($"Processor Count: {Environment.ProcessorCount}");
            report.AppendLine($"System Directory: {Environment.SystemDirectory}");
            report.AppendLine($"Working Directory: {Environment.CurrentDirectory}");
            report.AppendLine();
        }

        private static void AppendApplicationInformation(StringBuilder report)
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            var location = assembly.Location;

            report.AppendLine("APPLICATION INFORMATION");
            report.AppendLine("-".PadRight(40, '-'));
            report.AppendLine($"Version: {version}");
            report.AppendLine($"Location: {location}");
            report.AppendLine($"Build Date: {File.GetLastWriteTime(location):yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"Process ID: {Process.GetCurrentProcess().Id}");
            report.AppendLine($"Start Time: {Process.GetCurrentProcess().StartTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
        }

        private static void AppendRevitInformation(StringBuilder report, Document document)
        {
            try
            {
                report.AppendLine("REVIT INFORMATION");
                report.AppendLine("-".PadRight(40, '-'));
                report.AppendLine($"Document Title: {document.Title}");
                report.AppendLine($"Document Path: {document.PathName}");
                report.AppendLine($"Is Family Document: {document.IsFamilyDocument}");
                report.AppendLine($"Is Modified: {document.IsModified}");
                report.AppendLine($"Is Workshared: {document.IsWorkshared}");

                // Linked models
                var linkInstances = new FilteredElementCollector(document)
                    .OfClass(typeof(RevitLinkInstance))
                    .Cast<RevitLinkInstance>()
                    .ToList();

                report.AppendLine($"Linked Models: {linkInstances.Count}");
                foreach (var link in linkInstances.Take(10)) // Limit to first 10
                {
                    var linkDoc = link.GetLinkDocument();
                    report.AppendLine($"  - {linkDoc?.Title ?? link.Name} ({(linkDoc != null ? "Loaded" : "Unloaded")})");
                }

                // Levels
                var levels = new FilteredElementCollector(document)
                    .OfClass(typeof(Level))
                    .Cast<Level>()
                    .OrderBy(l => l.Elevation)
                    .ToList();

                report.AppendLine($"Levels: {levels.Count}");
                foreach (var level in levels.Take(10)) // Limit to first 10
                {
                    report.AppendLine($"  - {level.Name} (Elevation: {level.Elevation * 304.8:F0}mm)");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"Error getting Revit information: {ex.Message}");
            }
            
            report.AppendLine();
        }

        private static void AppendServiceContainerStatus(StringBuilder report)
        {
            report.AppendLine("SERVICE CONTAINER STATUS");
            report.AppendLine("-".PadRight(40, '-'));
            
            try
            {
                report.AppendLine($"Is Configured: {ServiceContainer.IsConfigured}");
                
                if (ServiceContainer.IsConfigured)
                {
                    var validation = ServiceContainer.GetValidationDetails();
                    report.AppendLine($"Is Valid: {validation.IsValid}");
                    report.AppendLine($"Registered Services: {validation.RegisteredServices.Count}");
                    
                    foreach (var service in validation.RegisteredServices)
                    {
                        report.AppendLine($"  ✓ {service}");
                    }
                    
                    if (validation.Issues.Count > 0)
                    {
                        report.AppendLine("Issues:");
                        foreach (var issue in validation.Issues)
                        {
                            report.AppendLine($"  ✗ {issue}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"Error checking service container: {ex.Message}");
            }
            
            report.AppendLine();
        }

        private static void AppendConfigurationStatus(StringBuilder report)
        {
            report.AppendLine("CONFIGURATION STATUS");
            report.AppendLine("-".PadRight(40, '-'));
            
            try
            {
                var settingsPath = AppSettings.GetSettingsFilePath();
                report.AppendLine($"Settings File: {settingsPath}");
                report.AppendLine($"Settings Exist: {AppSettings.SettingsFileExists()}");
                
                if (File.Exists(settingsPath))
                {
                    var fileInfo = new FileInfo(settingsPath);
                    report.AppendLine($"Settings Size: {fileInfo.Length} bytes");
                    report.AppendLine($"Last Modified: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
                }

                var preferences = UserPreferences.Instance;
                report.AppendLine($"Preferences Summary: {preferences.GetPreferencesSummary()}");
            }
            catch (Exception ex)
            {
                report.AppendLine($"Error checking configuration: {ex.Message}");
            }
            
            report.AppendLine();
        }

        private static async Task AppendPerformanceMetricsAsync(StringBuilder report)
        {
            report.AppendLine("PERFORMANCE METRICS");
            report.AppendLine("-".PadRight(40, '-'));
            
            try
            {
                var metrics = CollectPerformanceMetrics();
                
                report.AppendLine($"Working Set Memory: {metrics.WorkingSetMemoryMB} MB");
                report.AppendLine($"Private Memory: {metrics.PrivateMemoryMB} MB");
                report.AppendLine($"Managed Memory: {metrics.ManagedMemoryMB} MB");
                report.AppendLine($"Processor Time: {metrics.ProcessorTimeMs} ms");
                report.AppendLine($"Thread Count: {metrics.ThreadCount}");
                report.AppendLine($"Handle Count: {metrics.HandleCount}");
                report.AppendLine($"GC Gen 0: {metrics.GCGen0Collections}");
                report.AppendLine($"GC Gen 1: {metrics.GCGen1Collections}");
                report.AppendLine($"GC Gen 2: {metrics.GCGen2Collections}");
            }
            catch (Exception ex)
            {
                report.AppendLine($"Error collecting performance metrics: {ex.Message}");
            }
            
            report.AppendLine();
        }

        private static void AppendLogStatistics(StringBuilder report)
        {
            report.AppendLine("LOG STATISTICS");
            report.AppendLine("-".PadRight(40, '-'));
            
            try
            {
                var stats = Logger.Instance.GetStatistics();
                
                report.AppendLine($"Log File: {stats.LogFilePath}");
                report.AppendLine($"Log File Size: {stats.LogFileSizeFormatted}");
                report.AppendLine($"Queued Entries: {stats.QueuedEntries}");
                report.AppendLine($"Minimum Level: {stats.MinimumLogLevel}");
                report.AppendLine($"Write to File: {stats.WriteToFile}");
                report.AppendLine($"Write to Debug: {stats.WriteToDebug}");
            }
            catch (Exception ex)
            {
                report.AppendLine($"Error getting log statistics: {ex.Message}");
            }
            
            report.AppendLine();
        }

        private static async Task AppendRecentErrorsAsync(StringBuilder report)
        {
            report.AppendLine("RECENT LOG ENTRIES (Last 20)");
            report.AppendLine("-".PadRight(40, '-'));
            
            try
            {
                var recentEntries = await Logger.Instance.GetRecentLogEntriesAsync(20);
                
                if (recentEntries.Length > 0)
                {
                    foreach (var entry in recentEntries)
                    {
                        report.AppendLine(entry);
                    }
                }
                else
                {
                    report.AppendLine("No recent log entries found.");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"Error getting recent log entries: {ex.Message}");
            }
            
            report.AppendLine();
        }

        private static void ValidateDotNetVersion(SystemValidationResult result)
        {
            var version = Environment.Version;
            if (version.Major < 6)
            {
                result.CriticalIssues.Add($".NET version {version} is not supported. Requires .NET 6.0 or later.");
            }
        }

        private static void ValidateMemory(SystemValidationResult result)
        {
            var process = Process.GetCurrentProcess();
            var workingSetMB = process.WorkingSet64 / (1024 * 1024);
            
            if (workingSetMB > 2048) // 2GB
            {
                result.Warnings.Add($"High memory usage detected: {workingSetMB} MB");
            }
        }

        private static void ValidateDiskSpace(SystemValidationResult result)
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var drive = new DriveInfo(Path.GetPathRoot(appDataPath) ?? "C:");
                
                var freeSpaceGB = drive.AvailableFreeSpace / (1024 * 1024 * 1024);
                
                if (freeSpaceGB < 1)
                {
                    result.CriticalIssues.Add($"Low disk space: {freeSpaceGB:F1} GB available");
                }
                else if (freeSpaceGB < 5)
                {
                    result.Warnings.Add($"Limited disk space: {freeSpaceGB:F1} GB available");
                }
            }
            catch (Exception ex)
            {
                result.Warnings.Add($"Could not check disk space: {ex.Message}");
            }
        }

        private static void ValidateDependencies(SystemValidationResult result)
        {
            var requiredAssemblies = new[]
            {
                "RevitAPI",
                "RevitAPIUI",
                "ClosedXML",
                "MaterialDesignThemes.Wpf",
                "CommunityToolkit.Mvvm"
            };

            foreach (var assemblyName in requiredAssemblies)
            {
                try
                {
                    Assembly.Load(assemblyName);
                }
                catch (Exception)
                {
                    result.CriticalIssues.Add($"Required assembly not found: {assemblyName}");
                }
            }
        }

        private static void ValidatePermissions(SystemValidationResult result)
        {
            try
            {
                var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "MEP.Pacifire");
                Directory.CreateDirectory(appDataPath);
                
                var testFile = Path.Combine(appDataPath, "test.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
            }
            catch (Exception ex)
            {
                result.CriticalIssues.Add($"Insufficient file system permissions: {ex.Message}");
            }
        }

        private static void ValidateServiceContainer(SystemValidationResult result)
        {
            if (!ServiceContainer.IsConfigured)
            {
                result.CriticalIssues.Add("Service container is not configured");
                return;
            }

            var validation = ServiceContainer.GetValidationDetails();
            if (!validation.IsValid)
            {
                result.CriticalIssues.AddRange(validation.Issues);
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// System validation result
    /// </summary>
    public class SystemValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> CriticalIssues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();

        public string Summary
        {
            get
            {
                if (IsValid && Warnings.Count == 0)
                    return "System validation passed with no issues.";
                
                var parts = new List<string>();
                if (!IsValid)
                    parts.Add($"{CriticalIssues.Count} critical issue(s)");
                if (Warnings.Count > 0)
                    parts.Add($"{Warnings.Count} warning(s)");
                
                return string.Join(", ", parts);
            }
        }
    }

    /// <summary>
    /// Performance metrics snapshot
    /// </summary>
    public class PerformanceMetrics
    {
        public long WorkingSetMemoryMB { get; set; }
        public long PrivateMemoryMB { get; set; }
        public long ManagedMemoryMB { get; set; }
        public long ProcessorTimeMs { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public int GCGen0Collections { get; set; }
        public int GCGen1Collections { get; set; }
        public int GCGen2Collections { get; set; }
        public DateTime Timestamp { get; set; }
    }

    #endregion
}
