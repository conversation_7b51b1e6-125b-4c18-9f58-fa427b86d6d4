using System;
using System.Collections.Generic;
using Xunit;
using FluentAssertions;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Tests.Models
{
    /// <summary>
    /// Unit tests for FireStoppingElement model class.
    /// Tests data validation, property changes, and business logic.
    /// </summary>
    public class FireStoppingElementTests
    {
        [Fact]
        public void Constructor_WithValidElementId_SetsProperties()
        {
            // Arrange
            var elementId = new ElementId(12345);

            // Act
            var element = new FireStoppingElement(elementId);

            // Assert
            element.ElementId.Should().Be(elementId);
            element.DesignCheckResult.Should().NotBeNull();
            element.CustomParameters.Should().NotBeNull();
            element.CustomParameters.Should().BeEmpty();
        }

        [Fact]
        public void DisplayName_WithFamilyAndTypeName_ReturnsFormattedString()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1))
            {
                FamilyName = "Fire Stop Family",
                TypeName = "Type A"
            };

            // Act
            var displayName = element.DisplayName;

            // Assert
            displayName.Should().Be("Fire Stop Family - Type A");
        }

        [Fact]
        public void DisplayName_WithOnlyFamilyName_ReturnsFamilyName()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1))
            {
                FamilyName = "Fire Stop Family",
                TypeName = ""
            };

            // Act
            var displayName = element.DisplayName;

            // Assert
            displayName.Should().Be("Fire Stop Family");
        }

        [Fact]
        public void DisplayName_WithEmptyNames_ReturnsElementId()
        {
            // Arrange
            var elementId = new ElementId(12345);
            var element = new FireStoppingElement(elementId)
            {
                FamilyName = "",
                TypeName = ""
            };

            // Act
            var displayName = element.DisplayName;

            // Assert
            displayName.Should().Be("Element 12345");
        }

        [Fact]
        public void HasFailures_WithNoFailures_ReturnsFalse()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));
            element.DesignCheckResult.NotTouchingWall = false;
            element.DesignCheckResult.NotTouchingService = false;
            element.DesignCheckResult.Clashing = false;
            element.DesignCheckResult.Adjacent = false;

            // Act
            var hasFailures = element.HasFailures;

            // Assert
            hasFailures.Should().BeFalse();
        }

        [Fact]
        public void HasFailures_WithOneFailure_ReturnsTrue()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));
            element.DesignCheckResult.NotTouchingWall = true; // This is a failure
            element.DesignCheckResult.NotTouchingService = false;
            element.DesignCheckResult.Clashing = false;
            element.DesignCheckResult.Adjacent = false;

            // Act
            var hasFailures = element.HasFailures;

            // Assert
            hasFailures.Should().BeTrue();
        }

        [Fact]
        public void IsValid_WithRequiredParameters_ReturnsTrue()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1))
            {
                FamilyName = "Test Family",
                TypeName = "Test Type",
                BecaTypeMark = "TM001"
            };

            // Act
            var isValid = element.IsValid();

            // Assert
            isValid.Should().BeTrue();
        }

        [Fact]
        public void IsValid_WithMissingRequiredParameters_ReturnsFalse()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1))
            {
                FamilyName = "", // Missing required parameter
                TypeName = "Test Type",
                BecaTypeMark = "TM001"
            };

            // Act
            var isValid = element.IsValid();

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public void Clone_WithCompleteElement_ReturnsDeepCopy()
        {
            // Arrange
            var original = new FireStoppingElement(new ElementId(1))
            {
                FamilyName = "Test Family",
                TypeName = "Test Type",
                BecaTypeMark = "TM001",
                BecaInstMark = "IM001",
                LocationPoint = new XYZ(1, 2, 3)
            };
            original.DesignCheckResult.NotTouchingWall = true;
            original.CustomParameters["CustomParam"] = "CustomValue";

            // Act
            var clone = original.Clone();

            // Assert
            clone.Should().NotBeSameAs(original);
            clone.ElementId.Should().Be(original.ElementId);
            clone.FamilyName.Should().Be(original.FamilyName);
            clone.TypeName.Should().Be(original.TypeName);
            clone.BecaTypeMark.Should().Be(original.BecaTypeMark);
            clone.BecaInstMark.Should().Be(original.BecaInstMark);
            clone.LocationPoint.Should().Be(original.LocationPoint);
            clone.DesignCheckResult.NotTouchingWall.Should().Be(original.DesignCheckResult.NotTouchingWall);
            clone.CustomParameters["CustomParam"].Should().Be("CustomValue");
            
            // Verify deep copy - changes to clone shouldn't affect original
            clone.FamilyName = "Modified Family";
            original.FamilyName.Should().Be("Test Family");
        }

        [Fact]
        public void SetCustomParameter_WithValidKeyValue_AddsParameter()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));
            var key = "TestParameter";
            var value = "TestValue";

            // Act
            element.SetCustomParameter(key, value);

            // Assert
            element.CustomParameters.Should().ContainKey(key);
            element.CustomParameters[key].Should().Be(value);
        }

        [Fact]
        public void SetCustomParameter_WithNullKey_ThrowsException()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => element.SetCustomParameter(null!, "value"));
        }

        [Fact]
        public void SetCustomParameter_WithEmptyKey_ThrowsException()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));

            // Act & Assert
            Assert.Throws<ArgumentException>(() => element.SetCustomParameter("", "value"));
        }

        [Fact]
        public void GetCustomParameter_WithExistingKey_ReturnsValue()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));
            element.CustomParameters["TestKey"] = "TestValue";

            // Act
            var value = element.GetCustomParameter("TestKey");

            // Assert
            value.Should().Be("TestValue");
        }

        [Fact]
        public void GetCustomParameter_WithNonExistingKey_ReturnsNull()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));

            // Act
            var value = element.GetCustomParameter("NonExistingKey");

            // Assert
            value.Should().BeNull();
        }

        [Fact]
        public void GetCustomParameter_WithDefaultValue_ReturnsDefaultForMissingKey()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));
            var defaultValue = "DefaultValue";

            // Act
            var value = element.GetCustomParameter("NonExistingKey", defaultValue);

            // Assert
            value.Should().Be(defaultValue);
        }

        [Theory]
        [InlineData("", "", "", false)]
        [InlineData("Family", "", "", false)]
        [InlineData("", "Type", "", false)]
        [InlineData("", "", "TM001", false)]
        [InlineData("Family", "Type", "", false)]
        [InlineData("Family", "", "TM001", false)]
        [InlineData("", "Type", "TM001", false)]
        [InlineData("Family", "Type", "TM001", true)]
        public void IsValid_WithVariousParameterCombinations_ReturnsExpectedResult(
            string familyName, string typeName, string becaTypeMark, bool expectedValid)
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1))
            {
                FamilyName = familyName,
                TypeName = typeName,
                BecaTypeMark = becaTypeMark
            };

            // Act
            var isValid = element.IsValid();

            // Assert
            isValid.Should().Be(expectedValid);
        }

        [Fact]
        public void PropertyChanged_WhenFamilyNameChanges_RaisesEvent()
        {
            // Arrange
            var element = new FireStoppingElement(new ElementId(1));
            var eventRaised = false;
            element.PropertyChanged += (sender, args) =>
            {
                if (args.PropertyName == nameof(FireStoppingElement.FamilyName))
                    eventRaised = true;
            };

            // Act
            element.FamilyName = "New Family Name";

            // Assert
            eventRaised.Should().BeTrue();
        }

        [Fact]
        public void Equals_WithSameElementId_ReturnsTrue()
        {
            // Arrange
            var elementId = new ElementId(12345);
            var element1 = new FireStoppingElement(elementId);
            var element2 = new FireStoppingElement(elementId);

            // Act
            var areEqual = element1.Equals(element2);

            // Assert
            areEqual.Should().BeTrue();
        }

        [Fact]
        public void Equals_WithDifferentElementId_ReturnsFalse()
        {
            // Arrange
            var element1 = new FireStoppingElement(new ElementId(1));
            var element2 = new FireStoppingElement(new ElementId(2));

            // Act
            var areEqual = element1.Equals(element2);

            // Assert
            areEqual.Should().BeFalse();
        }

        [Fact]
        public void GetHashCode_WithSameElementId_ReturnsSameHash()
        {
            // Arrange
            var elementId = new ElementId(12345);
            var element1 = new FireStoppingElement(elementId);
            var element2 = new FireStoppingElement(elementId);

            // Act
            var hash1 = element1.GetHashCode();
            var hash2 = element2.GetHashCode();

            // Assert
            hash1.Should().Be(hash2);
        }
    }
}
